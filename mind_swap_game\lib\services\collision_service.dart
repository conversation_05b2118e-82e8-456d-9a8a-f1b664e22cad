import 'dart:collection';
import '../models/models.dart';

class CollisionService {
  // Check if a position is safe (no traps, walls, etc.)
  static bool isSafePosition(Maze maze, int x, int y) {
    if (!maze.isValidPosition(x, y)) {
      return false;
    }

    final element = maze.getElementAt(x, y);
    
    switch (element.type) {
      case PuzzleElementType.wall:
        return false;
      case PuzzleElementType.trap:
        return element.properties['triggered'] == true; // Safe if already triggered
      case PuzzleElementType.door:
        return element.state == ElementState.unlocked;
      default:
        return true;
    }
  }

  // Check if a position is walkable (considering player's keys)
  static bool isWalkableForPlayer(Maze maze, Player player, int x, int y) {
    if (!maze.isValidPosition(x, y)) {
      return false;
    }

    final element = maze.getElementAt(x, y);
    
    switch (element.type) {
      case PuzzleElementType.wall:
        return false;
      case PuzzleElementType.door:
        if (element.state == ElementState.unlocked) {
          return true;
        }
        final requiredKeyId = element.properties['requiredKeyId'] as String;
        return player.hasKey(requiredKeyId);
      default:
        return true;
    }
  }

  // Get all positions adjacent to a given position
  static List<Position> getAdjacentPositions(int x, int y) {
    return [
      Position(x + 1, y),     // Right
      Position(x - 1, y),     // Left
      Position(x, y + 1),     // Down
      Position(x, y - 1),     // Up
    ];
  }

  // Get all diagonal positions adjacent to a given position
  static List<Position> getDiagonalPositions(int x, int y) {
    return [
      Position(x + 1, y + 1), // Bottom-right
      Position(x + 1, y - 1), // Top-right
      Position(x - 1, y + 1), // Bottom-left
      Position(x - 1, y - 1), // Top-left
    ];
  }

  // Get all positions within a certain radius
  static List<Position> getPositionsInRadius(int centerX, int centerY, int radius) {
    final positions = <Position>[];
    
    for (int x = centerX - radius; x <= centerX + radius; x++) {
      for (int y = centerY - radius; y <= centerY + radius; y++) {
        final distance = (x - centerX).abs() + (y - centerY).abs();
        if (distance <= radius) {
          positions.add(Position(x, y));
        }
      }
    }
    
    return positions;
  }

  // Check if two positions are adjacent (Manhattan distance = 1)
  static bool areAdjacent(Position pos1, Position pos2) {
    final distance = (pos1.x - pos2.x).abs() + (pos1.y - pos2.y).abs();
    return distance == 1;
  }

  // Calculate Manhattan distance between two positions
  static int manhattanDistance(Position pos1, Position pos2) {
    return (pos1.x - pos2.x).abs() + (pos1.y - pos2.y).abs();
  }

  // Calculate Euclidean distance between two positions
  static double euclideanDistance(Position pos1, Position pos2) {
    final dx = pos1.x - pos2.x;
    final dy = pos1.y - pos2.y;
    return (dx * dx + dy * dy).toDouble();
  }

  // Find shortest path using A* algorithm
  static List<Position>? findPath(
    Maze maze,
    Position start,
    Position goal, {
    Player? player,
    bool avoidTraps = true,
  }) {
    if (!maze.isValidPosition(start.x, start.y) || 
        !maze.isValidPosition(goal.x, goal.y)) {
      return null;
    }

    final openSet = PriorityQueue<_PathNode>((a, b) => a.fScore.compareTo(b.fScore));
    final closedSet = <Position>{};
    final gScore = <Position, int>{};
    final fScore = <Position, int>{};
    final cameFrom = <Position, Position>{};

    gScore[start] = 0;
    fScore[start] = manhattanDistance(start, goal);
    openSet.add(_PathNode(start, fScore[start]!));

    while (openSet.isNotEmpty) {
      final current = openSet.removeFirst().position;

      if (current == goal) {
        // Reconstruct path
        final path = <Position>[];
        Position? node = goal;
        
        while (node != null) {
          path.insert(0, node);
          node = cameFrom[node];
        }
        
        return path;
      }

      closedSet.add(current);

      for (final neighbor in getAdjacentPositions(current.x, current.y)) {
        if (closedSet.contains(neighbor)) {
          continue;
        }

        if (!maze.isValidPosition(neighbor.x, neighbor.y)) {
          continue;
        }

        // Check if neighbor is walkable
        bool walkable;
        if (player != null) {
          walkable = isWalkableForPlayer(maze, player, neighbor.x, neighbor.y);
        } else {
          walkable = maze.isWalkable(neighbor.x, neighbor.y);
        }

        if (!walkable) {
          continue;
        }

        // Avoid traps if requested
        if (avoidTraps) {
          final element = maze.getElementAt(neighbor.x, neighbor.y);
          if (element.type == PuzzleElementType.trap && 
              element.properties['triggered'] != true) {
            continue;
          }
        }

        final tentativeGScore = gScore[current]! + 1;

        if (!gScore.containsKey(neighbor) || tentativeGScore < gScore[neighbor]!) {
          cameFrom[neighbor] = current;
          gScore[neighbor] = tentativeGScore;
          fScore[neighbor] = tentativeGScore + manhattanDistance(neighbor, goal);

          if (!openSet.any((node) => node.position == neighbor)) {
            openSet.add(_PathNode(neighbor, fScore[neighbor]!));
          }
        }
      }
    }

    return null; // No path found
  }

  // Check if there's a clear line of sight between two positions
  static bool hasLineOfSight(Maze maze, Position start, Position end) {
    final path = _bresenhamLine(start.x, start.y, end.x, end.y);
    
    for (final pos in path) {
      if (!maze.isValidPosition(pos.x, pos.y)) {
        return false;
      }
      
      final element = maze.getElementAt(pos.x, pos.y);
      if (element.type == PuzzleElementType.wall) {
        return false;
      }
    }
    
    return true;
  }

  // Bresenham's line algorithm for line of sight
  static List<Position> _bresenhamLine(int x0, int y0, int x1, int y1) {
    final points = <Position>[];
    
    final dx = (x1 - x0).abs();
    final dy = (y1 - y0).abs();
    final sx = x0 < x1 ? 1 : -1;
    final sy = y0 < y1 ? 1 : -1;
    var err = dx - dy;
    
    var x = x0;
    var y = y0;
    
    while (true) {
      points.add(Position(x, y));
      
      if (x == x1 && y == y1) break;
      
      final e2 = 2 * err;
      if (e2 > -dy) {
        err -= dy;
        x += sx;
      }
      if (e2 < dx) {
        err += dx;
        y += sy;
      }
    }
    
    return points;
  }

  // Find all reachable positions from a starting position
  static Set<Position> findReachablePositions(
    Maze maze,
    Position start, {
    Player? player,
    int? maxDistance,
  }) {
    final reachable = <Position>{};
    final queue = Queue<Position>();
    final distances = <Position, int>{};
    
    queue.add(start);
    distances[start] = 0;
    reachable.add(start);
    
    while (queue.isNotEmpty) {
      final current = queue.removeFirst();
      final currentDistance = distances[current]!;
      
      if (maxDistance != null && currentDistance >= maxDistance) {
        continue;
      }
      
      for (final neighbor in getAdjacentPositions(current.x, current.y)) {
        if (reachable.contains(neighbor)) {
          continue;
        }
        
        if (!maze.isValidPosition(neighbor.x, neighbor.y)) {
          continue;
        }
        
        bool walkable;
        if (player != null) {
          walkable = isWalkableForPlayer(maze, player, neighbor.x, neighbor.y);
        } else {
          walkable = maze.isWalkable(neighbor.x, neighbor.y);
        }
        
        if (walkable) {
          reachable.add(neighbor);
          distances[neighbor] = currentDistance + 1;
          queue.add(neighbor);
        }
      }
    }
    
    return reachable;
  }

  // Check if a position is in a dead end
  static bool isDeadEnd(Maze maze, Position position, {Player? player}) {
    if (!maze.isValidPosition(position.x, position.y)) {
      return true;
    }
    
    int walkableNeighbors = 0;
    
    for (final neighbor in getAdjacentPositions(position.x, position.y)) {
      if (!maze.isValidPosition(neighbor.x, neighbor.y)) {
        continue;
      }
      
      bool walkable;
      if (player != null) {
        walkable = isWalkableForPlayer(maze, player, neighbor.x, neighbor.y);
      } else {
        walkable = maze.isWalkable(neighbor.x, neighbor.y);
      }
      
      if (walkable) {
        walkableNeighbors++;
      }
    }
    
    return walkableNeighbors <= 1;
  }

  // Find safe positions (avoiding traps and dead ends)
  static List<Position> findSafePositions(Maze maze, Position center, int radius) {
    final safePositions = <Position>[];
    final positions = getPositionsInRadius(center.x, center.y, radius);
    
    for (final pos in positions) {
      if (isSafePosition(maze, pos.x, pos.y) && !isDeadEnd(maze, pos)) {
        safePositions.add(pos);
      }
    }
    
    return safePositions;
  }
}

class _PathNode {
  final Position position;
  final int fScore;
  
  _PathNode(this.position, this.fScore);
}

// Simple priority queue implementation
class PriorityQueue<T> {
  final List<T> _items = [];
  final int Function(T, T) _compare;
  
  PriorityQueue(this._compare);
  
  void add(T item) {
    _items.add(item);
    _items.sort(_compare);
  }
  
  T removeFirst() {
    return _items.removeAt(0);
  }
  
  bool get isNotEmpty => _items.isNotEmpty;
  
  bool any(bool Function(T) test) {
    return _items.any(test);
  }
}
