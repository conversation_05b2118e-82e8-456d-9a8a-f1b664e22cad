import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../widgets/chat_widget.dart';
import '../widgets/ping_system.dart';

class CommunicationProvider extends ChangeNotifier {
  final List<ChatMessage> _messages = [];
  final List<Ping> _pings = [];
  final StreamController<ChatMessage> _messageController = 
      StreamController<ChatMessage>.broadcast();
  final StreamController<Ping> _pingController = 
      StreamController<Ping>.broadcast();

  Timer? _pingCleanupTimer;

  // Getters
  List<ChatMessage> get messages => List.unmodifiable(_messages);
  List<Ping> get pings => List.unmodifiable(_pings);
  Stream<ChatMessage> get messageStream => _messageController.stream;
  Stream<Ping> get pingStream => _pingController.stream;

  CommunicationProvider() {
    _startPingCleanup();
  }

  // Chat methods
  void sendMessage({
    required String playerId,
    required String playerName,
    required String message,
    ChatMessageType type = ChatMessageType.text,
  }) {
    final chatMessage = ChatMessage(
      id: const Uuid().v4(),
      playerId: playerId,
      playerName: playerName,
      message: message,
      timestamp: DateTime.now(),
      type: type,
    );

    _messages.add(chatMessage);
    _messageController.add(chatMessage);
    notifyListeners();

    if (kDebugMode) {
      print('Chat: [$playerName] $message');
    }
  }

  void sendSystemMessage(String message) {
    sendMessage(
      playerId: 'system',
      playerName: 'System',
      message: message,
      type: ChatMessageType.system,
    );
  }

  void sendQuickMessage({
    required String playerId,
    required String playerName,
    required String message,
  }) {
    sendMessage(
      playerId: playerId,
      playerName: playerName,
      message: message,
      type: ChatMessageType.text,
    );
  }

  void clearMessages() {
    _messages.clear();
    notifyListeners();
  }

  // Ping methods
  void createPing({
    required int x,
    required int y,
    required PingType type,
    required String message,
    required String playerId,
    Duration? duration,
  }) {
    final ping = Ping(
      id: const Uuid().v4(),
      x: x,
      y: y,
      type: type,
      message: message,
      timestamp: DateTime.now(),
      duration: duration ?? const Duration(seconds: 5),
      playerId: playerId,
    );

    _pings.add(ping);
    _pingController.add(ping);
    notifyListeners();

    if (kDebugMode) {
      print('Ping created: ${type.name} at ($x, $y) - $message');
    }
  }

  void removePing(String pingId) {
    _pings.removeWhere((ping) => ping.id == pingId);
    notifyListeners();
  }

  void clearPings() {
    _pings.clear();
    notifyListeners();
  }

  void clearExpiredPings() {
    final expiredPings = _pings.where((ping) => ping.isExpired).toList();
    for (final ping in expiredPings) {
      _pings.remove(ping);
    }
    if (expiredPings.isNotEmpty) {
      notifyListeners();
    }
  }

  // Convenience methods for common pings
  void pingAttention({
    required int x,
    required int y,
    required String playerId,
    String? customMessage,
  }) {
    createPing(
      x: x,
      y: y,
      type: PingType.attention,
      message: customMessage ?? 'Look here!',
      playerId: playerId,
    );
  }

  void pingDanger({
    required int x,
    required int y,
    required String playerId,
    String? customMessage,
  }) {
    createPing(
      x: x,
      y: y,
      type: PingType.danger,
      message: customMessage ?? 'Danger! Avoid this area!',
      playerId: playerId,
      duration: const Duration(seconds: 8), // Longer duration for danger
    );
  }

  void pingObjective({
    required int x,
    required int y,
    required String playerId,
    String? customMessage,
  }) {
    createPing(
      x: x,
      y: y,
      type: PingType.objective,
      message: customMessage ?? 'Important objective here!',
      playerId: playerId,
      duration: const Duration(seconds: 10), // Longer for objectives
    );
  }

  void pingDirection({
    required int x,
    required int y,
    required String playerId,
    String? customMessage,
  }) {
    createPing(
      x: x,
      y: y,
      type: PingType.direction,
      message: customMessage ?? 'Go this way!',
      playerId: playerId,
      duration: const Duration(seconds: 3), // Shorter for directions
    );
  }

  void pingExit({
    required int x,
    required int y,
    required String playerId,
    String? customMessage,
  }) {
    createPing(
      x: x,
      y: y,
      type: PingType.exit,
      message: customMessage ?? 'Exit is here!',
      playerId: playerId,
      duration: const Duration(seconds: 15), // Long duration for exit
    );
  }

  // Directional guidance methods
  void sendDirectionalGuidance({
    required String playerId,
    required String playerName,
    required String direction,
  }) {
    final directionEmojis = {
      'up': '⬆️',
      'down': '⬇️',
      'left': '⬅️',
      'right': '➡️',
      'stop': '⏹️',
    };

    final emoji = directionEmojis[direction.toLowerCase()] ?? '📍';
    sendMessage(
      playerId: playerId,
      playerName: playerName,
      message: '$emoji ${direction.toUpperCase()}',
      type: ChatMessageType.direction,
    );
  }

  void sendLocationUpdate({
    required String playerId,
    required String playerName,
    required int x,
    required int y,
  }) {
    sendMessage(
      playerId: playerId,
      playerName: playerName,
      message: 'Walker at position ($x, $y)',
      type: ChatMessageType.system,
    );
  }

  // Game event notifications
  void notifyKeyCollected({
    required String playerId,
    required String playerName,
    required String keyId,
  }) {
    sendSystemMessage('$playerName collected key: $keyId');
  }

  void notifyDoorUnlocked({
    required String playerId,
    required String playerName,
    required String keyId,
  }) {
    sendSystemMessage('$playerName unlocked door with key: $keyId');
  }

  void notifyTrapTriggered({
    required String playerId,
    required String playerName,
    required int livesRemaining,
  }) {
    sendSystemMessage('⚠️ $playerName triggered a trap! Lives: $livesRemaining');
  }

  void notifyRoleSwap({
    required String player1Name,
    required String player2Name,
    required int swapNumber,
  }) {
    sendSystemMessage('🔄 Role swap #$swapNumber! $player1Name ↔ $player2Name');
  }

  void notifyGameStart() {
    sendSystemMessage('🎮 Game started! Good luck!');
  }

  void notifyGameEnd({
    required bool victory,
    String? reason,
  }) {
    if (victory) {
      sendSystemMessage('🎉 Victory! You completed the maze!');
    } else {
      sendSystemMessage('💀 Game Over! ${reason ?? 'Better luck next time!'}');
    }
  }

  // Voice communication placeholder methods
  void startVoiceChat() {
    sendSystemMessage('🎤 Voice chat started');
    // TODO: Implement voice chat functionality
  }

  void stopVoiceChat() {
    sendSystemMessage('🔇 Voice chat stopped');
    // TODO: Implement voice chat functionality
  }

  void toggleMute() {
    sendSystemMessage('🔇 Microphone toggled');
    // TODO: Implement mute functionality
  }

  // Cleanup methods
  void _startPingCleanup() {
    _pingCleanupTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      clearExpiredPings();
    });
  }

  @override
  void dispose() {
    _pingCleanupTimer?.cancel();
    _messageController.close();
    _pingController.close();
    super.dispose();
  }

  // Statistics and analytics
  int get totalMessages => _messages.length;
  int get totalPings => _pings.length;
  
  Map<ChatMessageType, int> get messageTypeStats {
    final stats = <ChatMessageType, int>{};
    for (final message in _messages) {
      stats[message.type] = (stats[message.type] ?? 0) + 1;
    }
    return stats;
  }

  Map<PingType, int> get pingTypeStats {
    final stats = <PingType, int>{};
    for (final ping in _pings) {
      stats[ping.type] = (stats[ping.type] ?? 0) + 1;
    }
    return stats;
  }

  // Export/Import for multiplayer synchronization
  Map<String, dynamic> exportState() {
    return {
      'messages': _messages.map((m) => {
        'id': m.id,
        'playerId': m.playerId,
        'playerName': m.playerName,
        'message': m.message,
        'timestamp': m.timestamp.toIso8601String(),
        'type': m.type.name,
      }).toList(),
      'pings': _pings.map((p) => {
        'id': p.id,
        'x': p.x,
        'y': p.y,
        'type': p.type.name,
        'message': p.message,
        'timestamp': p.timestamp.toIso8601String(),
        'duration': p.duration.inMilliseconds,
        'playerId': p.playerId,
      }).toList(),
    };
  }

  void importState(Map<String, dynamic> state) {
    // Clear current state
    _messages.clear();
    _pings.clear();

    // Import messages
    final messages = state['messages'] as List<dynamic>? ?? [];
    for (final messageData in messages) {
      final message = ChatMessage(
        id: messageData['id'],
        playerId: messageData['playerId'],
        playerName: messageData['playerName'],
        message: messageData['message'],
        timestamp: DateTime.parse(messageData['timestamp']),
        type: ChatMessageType.values.firstWhere(
          (t) => t.name == messageData['type'],
          orElse: () => ChatMessageType.text,
        ),
      );
      _messages.add(message);
    }

    // Import pings
    final pings = state['pings'] as List<dynamic>? ?? [];
    for (final pingData in pings) {
      final ping = Ping(
        id: pingData['id'],
        x: pingData['x'],
        y: pingData['y'],
        type: PingType.values.firstWhere(
          (t) => t.name == pingData['type'],
          orElse: () => PingType.attention,
        ),
        message: pingData['message'],
        timestamp: DateTime.parse(pingData['timestamp']),
        duration: Duration(milliseconds: pingData['duration']),
        playerId: pingData['playerId'],
      );
      _pings.add(ping);
    }

    notifyListeners();
  }
}
