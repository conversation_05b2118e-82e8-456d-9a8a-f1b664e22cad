import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/models.dart';
import '../services/maze_generator.dart';
import '../services/game_interaction_service.dart';

class GameProvider extends ChangeNotifier {
  GameState? _gameState;
  Timer? _gameTimer;
  String? _currentPlayerId;
  bool _isConnected = false;
  String? _connectionError;

  // Getters
  GameState? get gameState => _gameState;
  String? get currentPlayerId => _currentPlayerId;
  bool get isConnected => _isConnected;
  String? get connectionError => _connectionError;
  
  Player? get currentPlayer => _gameState?.getPlayerById(_currentPlayerId ?? '');
  Player? get navigator => _gameState?.getNavigator();
  Player? get walker => _gameState?.getWalker();
  
  bool get isNavigator => currentPlayer?.role == PlayerRole.navigator;
  bool get isWalker => currentPlayer?.role == PlayerRole.walker;
  bool get isHost => currentPlayer?.isHost ?? false;
  
  bool get canStartGame => _gameState?.canStart ?? false;
  bool get isGameActive => _gameState?.isGameActive ?? false;
  bool get isGameOver => _gameState?.isGameOver ?? false;

  // Game lifecycle methods
  Future<void> createGame({
    required String playerName,
    MazeDifficulty difficulty = MazeDifficulty.medium,
  }) async {
    try {
      _connectionError = null;
      
      // Generate maze
      final maze = MazeGenerator.generateMaze(difficulty: difficulty);
      
      // Create host player
      final hostPlayer = Player(
        name: playerName,
        role: PlayerRole.walker,
        status: PlayerStatus.ready,
        x: maze.startPosition.x,
        y: maze.startPosition.y,
        isHost: true,
      );
      
      // Create game state
      _gameState = GameState(
        maze: maze,
        players: [hostPlayer],
        status: GameStatus.waiting,
      );
      
      _currentPlayerId = hostPlayer.id;
      _isConnected = true;
      
      _gameState!.addToLog('Game created by ${hostPlayer.name}');
      notifyListeners();
    } catch (e) {
      _connectionError = 'Failed to create game: $e';
      notifyListeners();
    }
  }

  Future<void> joinGame({
    required String roomCode,
    required String playerName,
  }) async {
    try {
      _connectionError = null;

      // TODO: In a real implementation, this would connect to a server
      // For now, we'll create a simple local multiplayer simulation

      // Create a new game for testing if none exists
      if (_gameState == null) {
        await createGame(playerName: 'Host Player');
      }

      if (_gameState?.roomCode == roomCode && _gameState!.players.length < 2) {
        final newPlayer = Player(
          name: playerName,
          role: PlayerRole.navigator,
          status: PlayerStatus.ready,
          x: _gameState!.maze.startPosition.x,
          y: _gameState!.maze.startPosition.y,
        );

        _gameState!.addPlayer(newPlayer);
        _currentPlayerId = newPlayer.id;
        _isConnected = true;

        notifyListeners();
      } else {
        throw Exception('Room not found or full');
      }
    } catch (e) {
      _connectionError = 'Failed to join game: $e';
      notifyListeners();
    }
  }

  void startGame() {
    if (_gameState != null && canStartGame) {
      _gameState!.startGame();
      _startGameTimer();
      notifyListeners();
    }
  }

  void pauseGame() {
    if (_gameState != null && isGameActive) {
      _gameState!.pauseGame();
      _stopGameTimer();
      notifyListeners();
    }
  }

  void resumeGame() {
    if (_gameState != null && _gameState!.status == GameStatus.paused) {
      _gameState!.resumeGame();
      _startGameTimer();
      notifyListeners();
    }
  }

  void endGame(GameEndReason reason) {
    if (_gameState != null) {
      _gameState!.endGame(reason);
      _stopGameTimer();
      notifyListeners();
    }
  }

  void leaveGame() {
    if (_gameState != null && _currentPlayerId != null) {
      _gameState!.removePlayer(_currentPlayerId!);
      _cleanup();
      notifyListeners();
    }
  }

  // Player actions
  bool movePlayer(int deltaX, int deltaY) {
    if (_gameState == null || currentPlayer == null || !isWalker) {
      return false;
    }

    final newX = currentPlayer!.x + deltaX;
    final newY = currentPlayer!.y + deltaY;

    // Use interaction service for movement
    final result = GameInteractionService.processMovement(
      _gameState!,
      _currentPlayerId!,
      newX,
      newY,
    );

    if (result.isSuccess || result.isKeyCollected || result.isExitReached) {
      _handleInteractionResult(result);
      _checkGameEndConditions();
      notifyListeners();
      return true;
    }

    return false;
  }

  void moveUp() => movePlayer(0, -1);
  void moveDown() => movePlayer(0, 1);
  void moveLeft() => movePlayer(-1, 0);
  void moveRight() => movePlayer(1, 0);

  void interactWithElement(int x, int y) {
    if (_gameState == null || currentPlayer == null) return;

    final result = GameInteractionService.processDirectInteraction(
      _gameState!,
      _currentPlayerId!,
      x,
      y,
    );

    _handleInteractionResult(result);
    notifyListeners();
  }

  void _handleInteractionResult(InteractionResponse result) {
    if (_gameState == null) return;

    switch (result.result) {
      case InteractionResult.keyCollected:
        _gameState!.addToLog(result.message);
        break;
      case InteractionResult.doorUnlocked:
        _gameState!.addToLog(result.message);
        break;
      case InteractionResult.trapTriggered:
        _gameState!.totalLivesLost++;
        _gameState!.addToLog(result.message);
        break;
      case InteractionResult.pressurePlateActivated:
        _gameState!.addToLog(result.message);
        break;
      case InteractionResult.mirrorTileActivated:
        _gameState!.addToLog(result.message);
        break;
      case InteractionResult.exitReached:
        endGame(GameEndReason.victory);
        break;
      default:
        if (result.message.isNotEmpty) {
          _gameState!.addToLog(result.message);
        }
        break;
    }
  }

  // Timer management
  void _startGameTimer() {
    _stopGameTimer();
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_gameState != null && _gameState!.timer.isRunning) {
        _gameState!.timer.tick();
        
        // Check for role swap
        if (_gameState!.timer.shouldSwapRoles) {
          _swapPlayerRoles();
        }
        
        // Check for time up
        if (_gameState!.timer.isTimeUp) {
          endGame(GameEndReason.timeUp);
        }
        
        notifyListeners();
      }
    });
  }

  void _stopGameTimer() {
    _gameTimer?.cancel();
    _gameTimer = null;
  }

  void _swapPlayerRoles() {
    if (_gameState != null) {
      _gameState!.swapPlayerRoles();
      notifyListeners();
    }
  }

  // Game state checks
  void _checkGameEndConditions() {
    if (_gameState == null) return;

    final walker = _gameState!.getWalker();
    if (walker != null) {
      final currentElement = _gameState!.maze.getElementAt(walker.x, walker.y);
      
      // Check if reached exit with all keys
      if (currentElement.type == PuzzleElementType.exit) {
        final hasAllKeys = _gameState!.maze.requiredKeys
            .every((keyId) => walker.hasKey(keyId));
        
        if (hasAllKeys) {
          endGame(GameEndReason.victory);
        }
      }
    }

    // Check if out of lives
    if (!_gameState!.hasLivesRemaining) {
      endGame(GameEndReason.outOfLives);
    }
  }

  // Utility methods
  void updatePlayerStatus(PlayerStatus status) {
    if (currentPlayer != null) {
      final updatedPlayer = currentPlayer!.copyWith(status: status);
      final playerIndex = _gameState!.players.indexWhere((p) => p.id == _currentPlayerId);
      if (playerIndex != -1) {
        _gameState!.players[playerIndex] = updatedPlayer;
        notifyListeners();
      }
    }
  }

  void regenerateMaze({MazeDifficulty? difficulty}) {
    if (_gameState != null && _gameState!.status == GameStatus.waiting) {
      final newMaze = MazeGenerator.generateMaze(
        difficulty: difficulty ?? _gameState!.maze.difficulty
      );
      
      _gameState = _gameState!.copyWith(maze: newMaze);
      
      // Reset player positions
      for (final player in _gameState!.players) {
        player.x = newMaze.startPosition.x;
        player.y = newMaze.startPosition.y;
      }
      
      _gameState!.addToLog('Maze regenerated');
      notifyListeners();
    }
  }

  String getFormattedTime() {
    if (_gameState?.timer == null) return '00:00';
    
    final remainingTime = _gameState!.timer.remainingTime;
    final minutes = remainingTime ~/ 60;
    final seconds = remainingTime % 60;
    
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  String getFormattedSwapTime() {
    if (_gameState?.timer == null) return '00';
    
    final swapTime = _gameState!.timer.timeUntilSwap;
    return swapTime.toString().padLeft(2, '0');
  }

  bool get isSwapWarning => _gameState?.timer.isSwapWarning ?? false;

  // Debug methods
  void debugTriggerRoleSwap() {
    if (kDebugMode && _gameState != null) {
      _swapPlayerRoles();
    }
  }

  void debugAddLife() {
    if (kDebugMode && currentPlayer != null) {
      currentPlayer!.lives++;
      notifyListeners();
    }
  }

  void debugRemoveLife() {
    if (kDebugMode && currentPlayer != null) {
      _gameState!.playerLostLife(_currentPlayerId!);
      _checkGameEndConditions();
    }
  }

  // Cleanup
  void _cleanup() {
    _stopGameTimer();
    _gameState = null;
    _currentPlayerId = null;
    _isConnected = false;
    _connectionError = null;
  }

  @override
  void dispose() {
    _cleanup();
    super.dispose();
  }
}
