import 'package:flutter/material.dart';

class GameEffects {
  // Simple key collection effect
  static Widget keyCollectionEffect({
    required Offset position,
    required VoidCallback onComplete,
  }) {
    return Positioned(
      left: position.dx,
      top: position.dy,
      child: _AnimatedKeyEffect(onComplete: onComplete),
    );
  }

  // Simple trap explosion effect
  static Widget trapExplosionEffect({
    required Offset position,
    required VoidCallback onComplete,
  }) {
    return Positioned(
      left: position.dx - 20,
      top: position.dy - 20,
      child: _AnimatedExplosionEffect(onComplete: onComplete),
    );
  }

  // Simple door unlock effect
  static Widget doorUnlockEffect({
    required Offset position,
    required VoidCallback onComplete,
  }) {
    return Positioned(
      left: position.dx,
      top: position.dy,
      child: _AnimatedDoorEffect(onComplete: onComplete),
    );
  }

  // Simple victory effect
  static Widget victoryEffect({
    required Size screenSize,
    required VoidCallback onComplete,
  }) {
    return Container(
      width: screenSize.width,
      height: screenSize.height,
      child: _AnimatedVictoryEffect(onComplete: onComplete),
    );
  }

  // Simple game over effect
  static Widget gameOverEffect({
    required Size screenSize,
    required VoidCallback onComplete,
  }) {
    return Container(
      width: screenSize.width,
      height: screenSize.height,
      color: Colors.black.withOpacity(0.8),
      child: _AnimatedGameOverEffect(onComplete: onComplete),
    );
  }

  // Simple role swap effect
  static Widget roleSwapTransition({
    required Size screenSize,
    required String fromRole,
    required String toRole,
    required VoidCallback onComplete,
  }) {
    return Container(
      width: screenSize.width,
      height: screenSize.height,
      color: Colors.purple.withOpacity(0.9),
      child: _AnimatedRoleSwapEffect(
        fromRole: fromRole,
        toRole: toRole,
        onComplete: onComplete,
      ),
    );
  }

  // Simple pressure plate effect
  static Widget pressurePlateEffect({
    required Offset position,
    required VoidCallback onComplete,
  }) {
    return Positioned(
      left: position.dx - 10,
      top: position.dy - 10,
      child: _AnimatedPressurePlateEffect(onComplete: onComplete),
    );
  }

  // Simple warning flash effect
  static Widget warningFlash({
    required Size screenSize,
    required VoidCallback onComplete,
  }) {
    return Container(
      width: screenSize.width,
      height: screenSize.height,
      child: _AnimatedWarningFlash(onComplete: onComplete),
    );
  }
}

// Simple placeholder effects
class _AnimatedKeyEffect extends StatefulWidget {
  final VoidCallback onComplete;
  const _AnimatedKeyEffect({required this.onComplete});
  @override
  State<_AnimatedKeyEffect> createState() => _AnimatedKeyEffectState();
}

class _AnimatedKeyEffectState extends State<_AnimatedKeyEffect> {
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(milliseconds: 800), widget.onComplete);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: Colors.yellow.withOpacity(0.3),
        shape: BoxShape.circle,
      ),
      child: const Icon(Icons.key, color: Colors.amber, size: 24),
    );
  }
}

class _AnimatedExplosionEffect extends StatefulWidget {
  final VoidCallback onComplete;
  const _AnimatedExplosionEffect({required this.onComplete});
  @override
  State<_AnimatedExplosionEffect> createState() => _AnimatedExplosionEffectState();
}

class _AnimatedExplosionEffectState extends State<_AnimatedExplosionEffect> {
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(milliseconds: 600), widget.onComplete);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.8),
        shape: BoxShape.circle,
      ),
      child: const Icon(Icons.warning, color: Colors.white, size: 40),
    );
  }
}

class _AnimatedDoorEffect extends StatefulWidget {
  final VoidCallback onComplete;
  const _AnimatedDoorEffect({required this.onComplete});
  @override
  State<_AnimatedDoorEffect> createState() => _AnimatedDoorEffectState();
}

class _AnimatedDoorEffectState extends State<_AnimatedDoorEffect> {
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(milliseconds: 500), widget.onComplete);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.3),
        shape: BoxShape.circle,
      ),
      child: const Icon(Icons.lock_open, color: Colors.green, size: 24),
    );
  }
}

class _AnimatedVictoryEffect extends StatefulWidget {
  final VoidCallback onComplete;
  const _AnimatedVictoryEffect({required this.onComplete});
  @override
  State<_AnimatedVictoryEffect> createState() => _AnimatedVictoryEffectState();
}

class _AnimatedVictoryEffectState extends State<_AnimatedVictoryEffect> {
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(seconds: 3), widget.onComplete);
  }

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('🎉', style: TextStyle(fontSize: 80)),
          SizedBox(height: 20),
          Text(
            'VICTORY!',
            style: TextStyle(
              fontSize: 48,
              fontWeight: FontWeight.bold,
              color: Colors.yellow,
            ),
          ),
        ],
      ),
    );
  }
}

class _AnimatedGameOverEffect extends StatefulWidget {
  final VoidCallback onComplete;
  const _AnimatedGameOverEffect({required this.onComplete});
  @override
  State<_AnimatedGameOverEffect> createState() => _AnimatedGameOverEffectState();
}

class _AnimatedGameOverEffectState extends State<_AnimatedGameOverEffect> {
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(seconds: 2), widget.onComplete);
  }

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('💀', style: TextStyle(fontSize: 80)),
          SizedBox(height: 20),
          Text(
            'GAME OVER',
            style: TextStyle(
              fontSize: 36,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
        ],
      ),
    );
  }
}

class _AnimatedRoleSwapEffect extends StatefulWidget {
  final String fromRole;
  final String toRole;
  final VoidCallback onComplete;
  
  const _AnimatedRoleSwapEffect({
    required this.fromRole,
    required this.toRole,
    required this.onComplete,
  });
  
  @override
  State<_AnimatedRoleSwapEffect> createState() => _AnimatedRoleSwapEffectState();
}

class _AnimatedRoleSwapEffectState extends State<_AnimatedRoleSwapEffect> {
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(seconds: 2), widget.onComplete);
  }

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.swap_horiz, size: 80, color: Colors.white),
          SizedBox(height: 20),
          Text(
            'ROLES SWAPPED!',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}

class _AnimatedPressurePlateEffect extends StatefulWidget {
  final VoidCallback onComplete;
  const _AnimatedPressurePlateEffect({required this.onComplete});
  @override
  State<_AnimatedPressurePlateEffect> createState() => _AnimatedPressurePlateEffectState();
}

class _AnimatedPressurePlateEffectState extends State<_AnimatedPressurePlateEffect> {
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(milliseconds: 800), widget.onComplete);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: Colors.purple.withOpacity(0.3),
        shape: BoxShape.circle,
        border: Border.all(color: Colors.purple, width: 2),
      ),
    );
  }
}

class _AnimatedWarningFlash extends StatefulWidget {
  final VoidCallback onComplete;
  const _AnimatedWarningFlash({required this.onComplete});
  @override
  State<_AnimatedWarningFlash> createState() => _AnimatedWarningFlashState();
}

class _AnimatedWarningFlashState extends State<_AnimatedWarningFlash> {
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(milliseconds: 400), widget.onComplete);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.red.withOpacity(0.3),
    );
  }
}
