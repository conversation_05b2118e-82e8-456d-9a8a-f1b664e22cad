import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/models.dart';
import '../providers/providers.dart';
import '../widgets/navigator_view.dart';
import '../widgets/walker_view.dart';
import '../widgets/role_swap_indicator.dart';
import '../services/role_swap_service.dart';

class GameScreen extends StatefulWidget {
  const GameScreen({super.key});

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> {
  late RoleSwapService _roleSwapService;

  @override
  void initState() {
    super.initState();
    _roleSwapService = RoleSwapService();
    
    // Listen to role swap events
    _roleSwapService.eventStream.listen(_onRoleSwapEvent);
  }

  @override
  void dispose() {
    _roleSwapService.dispose();
    super.dispose();
  }

  void _onRoleSwapEvent(RoleSwapEvent event) {
    final gameProvider = context.read<GameProvider>();
    final audioProvider = context.read<AudioProvider>();
    
    switch (event.phase) {
      case SwapPhase.warning:
        audioProvider.playRoleSwapWarning();
        break;
      case SwapPhase.swapping:
        audioProvider.onRoleSwap();
        break;
      case SwapPhase.complete:
        // Role swap is handled by the game provider
        break;
      default:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<GameProvider>(
        builder: (context, gameProvider, child) {
          final gameState = gameProvider.gameState;
          
          if (gameState == null) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return Stack(
            children: [
              _buildGameView(gameProvider),
              _buildGameOverlay(gameProvider),
              _buildRoleSwapOverlay(gameProvider),
            ],
          );
        },
      ),
    );
  }

  Widget _buildGameView(GameProvider gameProvider) {
    final currentPlayer = gameProvider.currentPlayer;
    final gameState = gameProvider.gameState!;
    
    if (currentPlayer == null) {
      return const Center(
        child: Text('Waiting for player assignment...'),
      );
    }

    // Sync role swap service with game timer
    _roleSwapService.syncWithGameTimer(gameState.timer);

    if (currentPlayer.role == PlayerRole.navigator) {
      return NavigatorView(
        maze: gameState.maze,
        walker: gameProvider.walker,
        navigator: gameProvider.navigator,
        onTileTap: (x, y) => _onNavigatorTileTap(gameProvider, x, y),
        onTileLongPress: (x, y) => _onNavigatorTileLongPress(gameProvider, x, y),
      );
    } else {
      return WalkerView(
        maze: gameState.maze,
        walker: gameProvider.walker,
        onMove: (deltaX, deltaY) => _onWalkerMove(gameProvider, deltaX, deltaY),
        onInteract: (x, y) => _onWalkerInteract(gameProvider, x, y),
      );
    }
  }

  Widget _buildGameOverlay(GameProvider gameProvider) {
    final gameState = gameProvider.gameState!;
    
    return SafeArea(
      child: Column(
        children: [
          _buildTopBar(gameProvider),
          const Spacer(),
          if (gameProvider.isGameActive) _buildBottomControls(gameProvider),
        ],
      ),
    );
  }

  Widget _buildTopBar(GameProvider gameProvider) {
    final gameState = gameProvider.gameState!;
    final currentPlayer = gameProvider.currentPlayer;
    
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Game timer
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Time',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
              Text(
                gameProvider.getFormattedTime(),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(width: 16),
          
          // Role swap indicator
          Expanded(
            child: RoleSwapIndicator(
              roleSwapService: _roleSwapService,
              currentRole: currentPlayer?.role,
              onSwapComplete: () {
                // Handle role swap completion
                setState(() {});
              },
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Lives indicator
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              const Text(
                'Lives',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
              Row(
                children: List.generate(
                  gameState.totalLives.clamp(0, 5),
                  (index) => const Icon(
                    Icons.favorite,
                    color: Colors.red,
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBottomControls(GameProvider gameProvider) {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildControlButton(
            Icons.pause,
            'Pause',
            () => gameProvider.pauseGame(),
          ),
          _buildControlButton(
            Icons.chat,
            'Chat',
            () => _showChatDialog(),
          ),
          _buildControlButton(
            Icons.help,
            'Help',
            () => _showHelpDialog(),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton(IconData icon, String label, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16),
            const SizedBox(width: 4),
            Text(
              label,
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleSwapOverlay(GameProvider gameProvider) {
    final currentPlayer = gameProvider.currentPlayer;
    final walker = gameProvider.walker;
    final navigator = gameProvider.navigator;
    
    PlayerRole? newRole;
    if (currentPlayer?.role == PlayerRole.walker) {
      newRole = PlayerRole.navigator;
    } else if (currentPlayer?.role == PlayerRole.navigator) {
      newRole = PlayerRole.walker;
    }

    return RoleSwapOverlay(
      roleSwapService: _roleSwapService,
      currentRole: currentPlayer?.role,
      newRole: newRole,
    );
  }

  // Event handlers
  void _onNavigatorTileTap(GameProvider gameProvider, int x, int y) {
    // Navigator can tap tiles to provide hints or mark locations
    // This could be used for communication features
    final audioProvider = context.read<AudioProvider>();
    audioProvider.playButtonPressSound();
  }

  void _onNavigatorTileLongPress(GameProvider gameProvider, int x, int y) {
    // Navigator can long press to place markers or send specific instructions
    _showNavigatorActionDialog(x, y);
  }

  void _onWalkerMove(GameProvider gameProvider, int deltaX, int deltaY) {
    final success = gameProvider.movePlayer(deltaX, deltaY);
    final audioProvider = context.read<AudioProvider>();
    
    if (success) {
      audioProvider.onPlayerMove();
    } else {
      // Play error sound or provide feedback for invalid move
      audioProvider.playButtonPressSound();
    }
  }

  void _onWalkerInteract(GameProvider gameProvider, int x, int y) {
    gameProvider.interactWithElement(x, y);
    final audioProvider = context.read<AudioProvider>();
    audioProvider.playButtonPressSound();
  }

  // Dialog methods
  void _showChatDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Communication'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Chat feature coming soon!'),
            SizedBox(height: 16),
            Text('For now, use voice communication or coordinate in person.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showHelpDialog() {
    final gameProvider = context.read<GameProvider>();
    final isNavigator = gameProvider.isNavigator;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isNavigator ? 'Navigator Help' : 'Walker Help'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (isNavigator) ...[
              const Text('As Navigator:'),
              const Text('• You can see the entire maze'),
              const Text('• Guide the Walker to collect keys'),
              const Text('• Help them avoid traps'),
              const Text('• Lead them to the exit'),
              const Text('• Roles swap every 60 seconds'),
            ] else ...[
              const Text('As Walker:'),
              const Text('• You have limited vision'),
              const Text('• Follow Navigator\'s instructions'),
              const Text('• Collect keys to unlock doors'),
              const Text('• Avoid red trap tiles'),
              const Text('• Reach the exit to win'),
              const Text('• Roles swap every 60 seconds'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  void _showNavigatorActionDialog(int x, int y) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Tile ($x, $y)'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Navigator actions coming soon!'),
            SizedBox(height: 8),
            Text('Future features:'),
            Text('• Place markers'),
            Text('• Send directional hints'),
            Text('• Highlight important tiles'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
