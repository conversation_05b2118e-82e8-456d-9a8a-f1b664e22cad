import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../models/models.dart';

enum PingType {
  attention,    // General attention ping
  danger,       // Warning about traps
  objective,    // Point to keys, doors, etc.
  direction,    // Movement direction
  exit,         // Point to exit
}

class Ping {
  final String id;
  final int x;
  final int y;
  final PingType type;
  final String message;
  final DateTime timestamp;
  final Duration duration;
  final String playerId;

  Ping({
    required this.id,
    required this.x,
    required this.y,
    required this.type,
    required this.message,
    required this.timestamp,
    this.duration = const Duration(seconds: 5),
    required this.playerId,
  });

  bool get isExpired => DateTime.now().isAfter(timestamp.add(duration));
}

class PingSystem extends StatefulWidget {
  final List<Ping> pings;
  final Function(Ping ping)? onPingCreated;
  final Function(String pingId)? onPingRemoved;
  final double tileSize;
  final int mazeWidth;
  final int mazeHeight;

  const PingSystem({
    super.key,
    required this.pings,
    this.onPingCreated,
    this.onPingRemoved,
    this.tileSize = 40.0,
    required this.mazeWidth,
    required this.mazeHeight,
  });

  @override
  State<PingSystem> createState() => _PingSystemState();
}

class _PingSystemState extends State<PingSystem>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _fadeController;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    )..repeat(reverse: true);

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: widget.pings.map((ping) => _buildPingWidget(ping)).toList(),
    );
  }

  Widget _buildPingWidget(Ping ping) {
    if (ping.isExpired) {
      // Remove expired pings
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onPingRemoved?.call(ping.id);
      });
      return const SizedBox.shrink();
    }

    final left = ping.x * widget.tileSize;
    final top = ping.y * widget.tileSize;

    return Positioned(
      left: left,
      top: top,
      child: _buildPingIndicator(ping),
    );
  }

  Widget _buildPingIndicator(Ping ping) {
    final config = _getPingConfig(ping.type);
    
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        final scale = 1.0 + 0.3 * _pulseController.value;
        final opacity = 0.7 + 0.3 * _pulseController.value;
        
        return Transform.scale(
          scale: scale,
          child: Container(
            width: widget.tileSize,
            height: widget.tileSize,
            decoration: BoxDecoration(
              color: config.color.withOpacity(opacity * 0.3),
              border: Border.all(
                color: config.color.withOpacity(opacity),
                width: 2,
              ),
              borderRadius: BorderRadius.circular(widget.tileSize / 2),
            ),
            child: Center(
              child: Icon(
                config.icon,
                color: config.color,
                size: widget.tileSize * 0.5,
              ),
            ),
          ),
        );
      },
    ).animate().fadeIn(duration: const Duration(milliseconds: 300));
  }

  PingConfig _getPingConfig(PingType type) {
    switch (type) {
      case PingType.attention:
        return PingConfig(
          icon: Icons.location_on,
          color: Colors.blue,
          message: 'Look here!',
        );
      case PingType.danger:
        return PingConfig(
          icon: Icons.warning,
          color: Colors.red,
          message: 'Danger!',
        );
      case PingType.objective:
        return PingConfig(
          icon: Icons.star,
          color: Colors.orange,
          message: 'Objective!',
        );
      case PingType.direction:
        return PingConfig(
          icon: Icons.arrow_forward,
          color: Colors.green,
          message: 'Go this way!',
        );
      case PingType.exit:
        return PingConfig(
          icon: Icons.flag,
          color: Colors.purple,
          message: 'Exit here!',
        );
    }
  }
}

class PingConfig {
  final IconData icon;
  final Color color;
  final String message;

  PingConfig({
    required this.icon,
    required this.color,
    required this.message,
  });
}

class PingCreationPanel extends StatefulWidget {
  final Function(int x, int y, PingType type, String message)? onCreatePing;
  final bool isVisible;

  const PingCreationPanel({
    super.key,
    this.onCreatePing,
    this.isVisible = false,
  });

  @override
  State<PingCreationPanel> createState() => _PingCreationPanelState();
}

class _PingCreationPanelState extends State<PingCreationPanel> {
  PingType _selectedType = PingType.attention;
  final TextEditingController _messageController = TextEditingController();

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Create Ping',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildPingTypeSelector(),
          const SizedBox(height: 12),
          _buildMessageInput(),
          const SizedBox(height: 12),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildPingTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Ping Type',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: PingType.values.map((type) {
            final config = _getPingConfig(type);
            final isSelected = _selectedType == type;
            
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedType = type;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? config.color.withOpacity(0.2) : Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected ? config.color : Colors.grey.shade300,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      config.icon,
                      size: 16,
                      color: isSelected ? config.color : Colors.grey.shade600,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      type.name.toUpperCase(),
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: isSelected ? config.color : Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildMessageInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Message (Optional)',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _messageController,
          decoration: InputDecoration(
            hintText: 'Add a message...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          maxLength: 50,
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () {
            _messageController.clear();
            setState(() {
              _selectedType = PingType.attention;
            });
          },
          child: const Text('Cancel'),
        ),
        const SizedBox(width: 8),
        ElevatedButton(
          onPressed: _createPing,
          child: const Text('Create Ping'),
        ),
      ],
    );
  }

  void _createPing() {
    final message = _messageController.text.trim();
    final defaultMessage = _getPingConfig(_selectedType).message;
    
    // For now, we'll create a ping at a default position
    // In a real implementation, this would be based on where the user tapped
    widget.onCreatePing?.call(
      5, // x position
      5, // y position
      _selectedType,
      message.isNotEmpty ? message : defaultMessage,
    );
    
    _messageController.clear();
  }

  PingConfig _getPingConfig(PingType type) {
    switch (type) {
      case PingType.attention:
        return PingConfig(
          icon: Icons.location_on,
          color: Colors.blue,
          message: 'Look here!',
        );
      case PingType.danger:
        return PingConfig(
          icon: Icons.warning,
          color: Colors.red,
          message: 'Danger!',
        );
      case PingType.objective:
        return PingConfig(
          icon: Icons.star,
          color: Colors.orange,
          message: 'Objective!',
        );
      case PingType.direction:
        return PingConfig(
          icon: Icons.arrow_forward,
          color: Colors.green,
          message: 'Go this way!',
        );
      case PingType.exit:
        return PingConfig(
          icon: Icons.flag,
          color: Colors.purple,
          message: 'Exit here!',
        );
    }
  }
}
