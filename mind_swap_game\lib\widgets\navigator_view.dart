import 'package:flutter/material.dart';
import '../models/models.dart';

class NavigatorView extends StatefulWidget {
  final Maze maze;
  final Player? walker;
  final Player? navigator;
  final Function(int x, int y)? onTileTap;
  final Function(int x, int y)? onTileLongPress;

  const NavigatorView({
    super.key,
    required this.maze,
    this.walker,
    this.navigator,
    this.onTileTap,
    this.onTileLongPress,
  });

  @override
  State<NavigatorView> createState() => _NavigatorViewState();
}

class _NavigatorViewState extends State<NavigatorView>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  double _scale = 1.0;
  Offset _panOffset = Offset.zero;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue.shade50,
            Colors.blue.shade100,
          ],
        ),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: _buildMazeView(),
                ),
                Container(
                  width: 200,
                  child: _buildNavigationPanel(),
                ),
              ],
            ),
          ),
          _buildLegend(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade600,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          const Icon(
            Icons.map,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 8),
          const Text(
            'Navigator View',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          if (widget.walker != null) ...[
            const Icon(
              Icons.directions_walk,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 4),
            Text(
              '(${widget.walker!.x}, ${widget.walker!.y})',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNavigationPanel() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade300),
              ),
            ),
            child: const Row(
              children: [
                Icon(Icons.chat, color: Colors.blue, size: 20),
                SizedBox(width: 8),
                Text(
                  'Guide Walker',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildQuickDirections(),
                  const SizedBox(height: 16),
                  _buildWalkerInfo(),
                  const SizedBox(height: 16),
                  _buildObjectiveInfo(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickDirections() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Directions',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        GridView.count(
          shrinkWrap: true,
          crossAxisCount: 3,
          childAspectRatio: 1.0,
          mainAxisSpacing: 4,
          crossAxisSpacing: 4,
          children: [
            Container(),
            _buildDirectionButton('↑', 'Up'),
            Container(),
            _buildDirectionButton('←', 'Left'),
            _buildDirectionButton('⏹', 'Stop'),
            _buildDirectionButton('→', 'Right'),
            Container(),
            _buildDirectionButton('↓', 'Down'),
            Container(),
          ],
        ),
      ],
    );
  }

  Widget _buildDirectionButton(String symbol, String direction) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        border: Border.all(color: Colors.blue.shade200),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Center(
        child: Text(
          symbol,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.blue.shade700,
          ),
        ),
      ),
    );
  }

  Widget _buildWalkerInfo() {
    if (widget.walker == null) {
      return Container();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Walker Status',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        _buildInfoRow('Position', '(${widget.walker!.x}, ${widget.walker!.y})'),
        _buildInfoRow('Lives', '${widget.walker!.lives}'),
        _buildInfoRow('Keys', '${widget.walker!.inventory.length}'),
      ],
    );
  }

  Widget _buildObjectiveInfo() {
    final requiredKeys = widget.maze.requiredKeys;
    final collectedKeys = widget.walker?.inventory ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Objectives',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        ...requiredKeys.map((keyId) {
          final isCollected = collectedKeys.contains(keyId);
          return _buildObjectiveItem(
            'Collect $keyId',
            isCollected,
          );
        }),
        _buildObjectiveItem(
          'Reach Exit',
          false, // This would be determined by game logic
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildObjectiveItem(String objective, bool isCompleted) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
            color: isCompleted ? Colors.green : Colors.grey,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              objective,
              style: TextStyle(
                fontSize: 12,
                color: isCompleted ? Colors.green : Colors.grey.shade700,
                decoration: isCompleted ? TextDecoration.lineThrough : null,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMazeView() {
    return GestureDetector(
      onScaleStart: (details) {
        // Handle zoom start
      },
      onScaleUpdate: (details) {
        setState(() {
          _scale = (_scale * details.scale).clamp(0.5, 3.0);
          _panOffset += details.focalPointDelta;
        });
      },
      child: Transform.scale(
        scale: _scale,
        child: Transform.translate(
          offset: _panOffset,
          child: Center(
            child: _buildMazeGrid(),
          ),
        ),
      ),
    );
  }

  Widget _buildMazeGrid() {
    final tileSize = 40.0;
    final gridWidth = widget.maze.width * tileSize;
    final gridHeight = widget.maze.height * tileSize;

    return Container(
      width: gridWidth,
      height: gridHeight,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade400, width: 2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: widget.maze.width,
          childAspectRatio: 1.0,
        ),
        itemCount: widget.maze.width * widget.maze.height,
        itemBuilder: (context, index) {
          final x = index % widget.maze.width;
          final y = index ~/ widget.maze.width;
          return _buildMazeTile(x, y);
        },
      ),
    );
  }

  Widget _buildMazeTile(int x, int y) {
    final element = widget.maze.getElementAt(x, y);
    final isWalkerPosition = widget.walker?.x == x && widget.walker?.y == y;
    final isNavigatorPosition = widget.navigator?.x == x && widget.navigator?.y == y;

    return GestureDetector(
      onTap: () => widget.onTileTap?.call(x, y),
      onLongPress: () => widget.onTileLongPress?.call(x, y),
      child: Container(
        margin: const EdgeInsets.all(0.5),
        decoration: BoxDecoration(
          color: _getTileColor(element),
          border: Border.all(
            color: _getTileBorderColor(element),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(2),
        ),
        child: Stack(
          children: [
            // Base tile content
            Center(child: _getTileIcon(element)),
            
            // Player indicators
            if (isWalkerPosition) _buildPlayerIndicator(PlayerRole.walker),
            if (isNavigatorPosition) _buildPlayerIndicator(PlayerRole.navigator),
            
            // Special effects
            if (_shouldPulse(element)) _buildPulseEffect(),
            
            // Interaction hints
            if (_canInteract(element)) _buildInteractionHint(),
          ],
        ),
      ),
    );
  }

  Widget _buildPlayerIndicator(PlayerRole role) {
    final isWalker = role == PlayerRole.walker;
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              color: (isWalker ? Colors.green : Colors.blue)
                  .withOpacity(0.3 + 0.2 * _pulseController.value),
              borderRadius: BorderRadius.circular(2),
              border: Border.all(
                color: isWalker ? Colors.green : Colors.blue,
                width: 2,
              ),
            ),
            child: Center(
              child: Icon(
                isWalker ? Icons.directions_walk : Icons.map,
                color: isWalker ? Colors.green : Colors.blue,
                size: 16,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPulseEffect() {
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              color: Colors.yellow.withOpacity(0.3 * _pulseController.value),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
        );
      },
    );
  }

  Widget _buildInteractionHint() {
    return Positioned(
      top: 2,
      right: 2,
      child: Container(
        width: 8,
        height: 8,
        decoration: const BoxDecoration(
          color: Colors.orange,
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  Color _getTileColor(PuzzleElement element) {
    switch (element.type) {
      case PuzzleElementType.wall:
        return Colors.grey.shade700;
      case PuzzleElementType.empty:
        return Colors.white;
      case PuzzleElementType.key:
        final color = element.properties['color'] as String? ?? 'gold';
        return _getKeyColor(color).withOpacity(0.3);
      case PuzzleElementType.door:
        final color = element.properties['color'] as String? ?? 'brown';
        return element.state == ElementState.unlocked
            ? Colors.green.withOpacity(0.3)
            : _getKeyColor(color).withOpacity(0.5);
      case PuzzleElementType.trap:
        return Colors.red.withOpacity(0.3);
      case PuzzleElementType.pressurePlate:
        return element.state == ElementState.active
            ? Colors.purple.withOpacity(0.5)
            : Colors.purple.withOpacity(0.2);
      case PuzzleElementType.mirrorTile:
        return Colors.cyan.withOpacity(0.3);
      case PuzzleElementType.fogZone:
        return Colors.grey.withOpacity(0.5);
      case PuzzleElementType.exit:
        return Colors.green.withOpacity(0.4);
      case PuzzleElementType.startPosition:
        return Colors.blue.withOpacity(0.3);
    }
  }

  Color _getTileBorderColor(PuzzleElement element) {
    switch (element.type) {
      case PuzzleElementType.wall:
        return Colors.grey.shade800;
      case PuzzleElementType.key:
        final color = element.properties['color'] as String? ?? 'gold';
        return _getKeyColor(color);
      case PuzzleElementType.door:
        final color = element.properties['color'] as String? ?? 'brown';
        return element.state == ElementState.unlocked
            ? Colors.green
            : _getKeyColor(color);
      case PuzzleElementType.trap:
        return Colors.red;
      case PuzzleElementType.pressurePlate:
        return Colors.purple;
      case PuzzleElementType.mirrorTile:
        return Colors.cyan;
      case PuzzleElementType.fogZone:
        return Colors.grey;
      case PuzzleElementType.exit:
        return Colors.green;
      default:
        return Colors.grey.shade300;
    }
  }

  Widget _getTileIcon(PuzzleElement element) {
    IconData? iconData;
    Color? iconColor;

    switch (element.type) {
      case PuzzleElementType.wall:
        return Container(); // No icon for walls
      case PuzzleElementType.key:
        iconData = Icons.key;
        final color = element.properties['color'] as String? ?? 'gold';
        iconColor = _getKeyColor(color);
        break;
      case PuzzleElementType.door:
        iconData = element.state == ElementState.unlocked
            ? Icons.door_front_door_outlined
            : Icons.lock;
        iconColor = element.state == ElementState.unlocked
            ? Colors.green
            : Colors.brown;
        break;
      case PuzzleElementType.trap:
        iconData = Icons.warning;
        iconColor = Colors.red;
        break;
      case PuzzleElementType.pressurePlate:
        iconData = Icons.radio_button_unchecked;
        iconColor = element.state == ElementState.active
            ? Colors.purple
            : Colors.purple.shade300;
        break;
      case PuzzleElementType.mirrorTile:
        iconData = Icons.flip;
        iconColor = Colors.cyan;
        break;
      case PuzzleElementType.fogZone:
        iconData = Icons.cloud;
        iconColor = Colors.grey;
        break;
      case PuzzleElementType.exit:
        iconData = Icons.flag;
        iconColor = Colors.green;
        break;
      default:
        return Container();
    }

    if (iconData != null) {
      return Icon(
        iconData,
        color: iconColor,
        size: 20,
      );
    }

    return Container();
  }

  Color _getKeyColor(String colorName) {
    switch (colorName.toLowerCase()) {
      case 'gold':
        return Colors.amber;
      case 'silver':
        return Colors.grey;
      case 'bronze':
        return Colors.brown;
      case 'red':
        return Colors.red;
      case 'blue':
        return Colors.blue;
      case 'green':
        return Colors.green;
      default:
        return Colors.amber;
    }
  }

  bool _shouldPulse(PuzzleElement element) {
    return element.type == PuzzleElementType.key && 
           element.properties['collected'] != true;
  }

  bool _canInteract(PuzzleElement element) {
    return element.isInteractable;
  }

  Widget _buildLegend() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Legend',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 16,
            runSpacing: 8,
            children: [
              _buildLegendItem(Icons.directions_walk, 'Walker', Colors.green),
              _buildLegendItem(Icons.key, 'Key', Colors.amber),
              _buildLegendItem(Icons.lock, 'Door', Colors.brown),
              _buildLegendItem(Icons.warning, 'Trap', Colors.red),
              _buildLegendItem(Icons.radio_button_unchecked, 'Plate', Colors.purple),
              _buildLegendItem(Icons.flag, 'Exit', Colors.green),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(IconData icon, String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade700,
          ),
        ),
      ],
    );
  }
}
