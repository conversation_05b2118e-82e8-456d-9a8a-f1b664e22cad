import 'package:uuid/uuid.dart';
import 'player.dart';
import 'maze.dart';
import 'puzzle_element.dart';

enum GameStatus {
  waiting,      // Waiting for players to join
  starting,     // Game is about to start
  playing,      // Game in progress
  paused,       // Game paused
  completed,    // Players won
  failed,       // Players lost
  disconnected, // Connection lost
}

enum GameEndReason {
  victory,
  outOfLives,
  timeUp,
  disconnection,
  playerQuit,
}

class GameTimer {
  final int totalTime;
  int remainingTime;
  final int roleSwapInterval;
  int timeUntilSwap;
  bool isRunning;

  GameTimer({
    required this.totalTime,
    this.roleSwapInterval = 60,
  }) : remainingTime = totalTime,
       timeUntilSwap = roleSwapInterval,
       isRunning = false;

  GameTimer copyWith({
    int? totalTime,
    int? remainingTime,
    int? roleSwapInterval,
    int? timeUntilSwap,
    bool? isRunning,
  }) {
    return GameTimer(
      totalTime: totalTime ?? this.totalTime,
      roleSwapInterval: roleSwapInterval ?? this.roleSwapInterval,
    )..remainingTime = remainingTime ?? this.remainingTime
     ..timeUntilSwap = timeUntilSwap ?? this.timeUntilSwap
     ..isRunning = isRunning ?? this.isRunning;
  }

  void tick() {
    if (!isRunning) return;
    
    if (remainingTime > 0) {
      remainingTime--;
    }
    
    if (timeUntilSwap > 0) {
      timeUntilSwap--;
    }
  }

  void start() {
    isRunning = true;
  }

  void pause() {
    isRunning = false;
  }

  void reset() {
    remainingTime = totalTime;
    timeUntilSwap = roleSwapInterval;
    isRunning = false;
  }

  void resetSwapTimer() {
    timeUntilSwap = roleSwapInterval;
  }

  bool get shouldSwapRoles => timeUntilSwap <= 0;
  bool get isTimeUp => remainingTime <= 0;
  bool get isSwapWarning => timeUntilSwap <= 10 && timeUntilSwap > 0;

  Map<String, dynamic> toJson() {
    return {
      'totalTime': totalTime,
      'remainingTime': remainingTime,
      'roleSwapInterval': roleSwapInterval,
      'timeUntilSwap': timeUntilSwap,
      'isRunning': isRunning,
    };
  }

  factory GameTimer.fromJson(Map<String, dynamic> json) {
    return GameTimer(
      totalTime: json['totalTime'],
      roleSwapInterval: json['roleSwapInterval'],
    )..remainingTime = json['remainingTime']
     ..timeUntilSwap = json['timeUntilSwap']
     ..isRunning = json['isRunning'];
  }
}

class GameState {
  final String id;
  final String roomCode;
  GameStatus status;
  final Maze maze;
  final List<Player> players;
  final GameTimer timer;
  final Map<String, dynamic> gameData;
  GameEndReason? endReason;
  final DateTime createdAt;
  DateTime? startedAt;
  DateTime? endedAt;
  int totalLivesLost;
  int totalRoleSwaps;
  List<String> gameLog;

  GameState({
    String? id,
    String? roomCode,
    this.status = GameStatus.waiting,
    required this.maze,
    List<Player>? players,
    GameTimer? timer,
    Map<String, dynamic>? gameData,
    this.endReason,
    DateTime? createdAt,
    this.startedAt,
    this.endedAt,
    this.totalLivesLost = 0,
    this.totalRoleSwaps = 0,
    List<String>? gameLog,
  }) : id = id ?? const Uuid().v4(),
       roomCode = roomCode ?? _generateRoomCode(),
       players = players ?? [],
       timer = timer ?? GameTimer(totalTime: maze.timeLimit),
       gameData = gameData ?? {},
       createdAt = createdAt ?? DateTime.now(),
       gameLog = gameLog ?? [];

  static String _generateRoomCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    return String.fromCharCodes(
      Iterable.generate(6, (_) => chars.codeUnitAt(random % chars.length))
    );
  }

  GameState copyWith({
    String? id,
    String? roomCode,
    GameStatus? status,
    Maze? maze,
    List<Player>? players,
    GameTimer? timer,
    Map<String, dynamic>? gameData,
    GameEndReason? endReason,
    DateTime? createdAt,
    DateTime? startedAt,
    DateTime? endedAt,
    int? totalLivesLost,
    int? totalRoleSwaps,
    List<String>? gameLog,
  }) {
    return GameState(
      id: id ?? this.id,
      roomCode: roomCode ?? this.roomCode,
      status: status ?? this.status,
      maze: maze ?? this.maze,
      players: players ?? List.from(this.players),
      timer: timer ?? this.timer,
      gameData: gameData ?? Map.from(this.gameData),
      endReason: endReason ?? this.endReason,
      createdAt: createdAt ?? this.createdAt,
      startedAt: startedAt ?? this.startedAt,
      endedAt: endedAt ?? this.endedAt,
      totalLivesLost: totalLivesLost ?? this.totalLivesLost,
      totalRoleSwaps: totalRoleSwaps ?? this.totalRoleSwaps,
      gameLog: gameLog ?? List.from(this.gameLog),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'roomCode': roomCode,
      'status': status.name,
      'maze': maze.toJson(),
      'players': players.map((p) => p.toJson()).toList(),
      'timer': timer.toJson(),
      'gameData': gameData,
      'endReason': endReason?.name,
      'createdAt': createdAt.toIso8601String(),
      'startedAt': startedAt?.toIso8601String(),
      'endedAt': endedAt?.toIso8601String(),
      'totalLivesLost': totalLivesLost,
      'totalRoleSwaps': totalRoleSwaps,
      'gameLog': gameLog,
    };
  }

  factory GameState.fromJson(Map<String, dynamic> json) {
    return GameState(
      id: json['id'],
      roomCode: json['roomCode'],
      status: GameStatus.values.firstWhere((e) => e.name == json['status']),
      maze: Maze.fromJson(json['maze']),
      players: (json['players'] as List).map((p) => Player.fromJson(p)).toList(),
      timer: GameTimer.fromJson(json['timer']),
      gameData: Map<String, dynamic>.from(json['gameData'] ?? {}),
      endReason: json['endReason'] != null 
          ? GameEndReason.values.firstWhere((e) => e.name == json['endReason'])
          : null,
      createdAt: DateTime.parse(json['createdAt']),
      startedAt: json['startedAt'] != null ? DateTime.parse(json['startedAt']) : null,
      endedAt: json['endedAt'] != null ? DateTime.parse(json['endedAt']) : null,
      totalLivesLost: json['totalLivesLost'] ?? 0,
      totalRoleSwaps: json['totalRoleSwaps'] ?? 0,
      gameLog: List<String>.from(json['gameLog'] ?? []),
    );
  }

  // Helper methods
  Player? getPlayerById(String playerId) {
    try {
      return players.firstWhere((p) => p.id == playerId);
    } catch (e) {
      return null;
    }
  }

  Player? getNavigator() {
    try {
      return players.firstWhere((p) => p.role == PlayerRole.navigator);
    } catch (e) {
      return null;
    }
  }

  Player? getWalker() {
    try {
      return players.firstWhere((p) => p.role == PlayerRole.walker);
    } catch (e) {
      return null;
    }
  }

  bool get canStart => players.length == 2 && 
                      players.every((p) => p.status == PlayerStatus.ready);

  bool get isGameActive => status == GameStatus.playing;

  bool get isGameOver => status == GameStatus.completed || 
                        status == GameStatus.failed;

  int get totalLives => players.fold(0, (sum, player) => sum + player.lives);

  bool get hasLivesRemaining => totalLives > 0;

  void addPlayer(Player player) {
    if (players.length < 2 && !players.any((p) => p.id == player.id)) {
      // Assign roles alternately
      if (players.isEmpty) {
        player.role = PlayerRole.walker;
        player.isHost = true;
      } else {
        player.role = PlayerRole.navigator;
      }
      players.add(player);
      addToLog('Player ${player.name} joined as ${player.role.name}');
    }
  }

  void removePlayer(String playerId) {
    players.removeWhere((p) => p.id == playerId);
    addToLog('Player left the game');
  }

  void swapPlayerRoles() {
    for (final player in players) {
      player.swapRole();
    }
    totalRoleSwaps++;
    timer.resetSwapTimer();
    addToLog('Player roles swapped! (Swap #$totalRoleSwaps)');
  }

  void startGame() {
    if (canStart) {
      status = GameStatus.playing;
      startedAt = DateTime.now();
      timer.start();
      addToLog('Game started!');
    }
  }

  void pauseGame() {
    if (status == GameStatus.playing) {
      status = GameStatus.paused;
      timer.pause();
      addToLog('Game paused');
    }
  }

  void resumeGame() {
    if (status == GameStatus.paused) {
      status = GameStatus.playing;
      timer.start();
      addToLog('Game resumed');
    }
  }

  void endGame(GameEndReason reason) {
    status = reason == GameEndReason.victory 
        ? GameStatus.completed 
        : GameStatus.failed;
    endReason = reason;
    endedAt = DateTime.now();
    timer.pause();
    addToLog('Game ended: ${reason.name}');
  }

  void playerLostLife(String playerId) {
    final player = getPlayerById(playerId);
    if (player != null) {
      player.loseLife();
      totalLivesLost++;
      addToLog('${player.name} lost a life! Lives remaining: ${player.lives}');
      
      if (!hasLivesRemaining) {
        endGame(GameEndReason.outOfLives);
      }
    }
  }

  bool movePlayer(String playerId, int newX, int newY) {
    final player = getPlayerById(playerId);
    if (player == null || player.role != PlayerRole.walker) {
      return false;
    }

    if (!maze.isWalkable(newX, newY)) {
      return false;
    }

    // Check for interactions
    final element = maze.getElementAt(newX, newY);
    _handleElementInteraction(player, element);

    // Update player position
    player.x = newX;
    player.y = newY;

    addToLog('${player.name} moved to ($newX, $newY)');
    return true;
  }

  void _handleElementInteraction(Player player, PuzzleElement element) {
    switch (element.type) {
      case PuzzleElementType.key:
        if (element.isCollectable) {
          final keyId = element.properties['keyId'] as String;
          player.addToInventory(keyId);
          element.collect();
          addToLog('${player.name} collected key: $keyId');
        }
        break;
      case PuzzleElementType.trap:
        if (!element.properties['triggered']) {
          element.trigger();
          playerLostLife(player.id);
        }
        break;
      case PuzzleElementType.pressurePlate:
        element.activate();
        addToLog('${player.name} activated pressure plate');
        break;
      case PuzzleElementType.exit:
        if (_hasRequiredKeys(player)) {
          endGame(GameEndReason.victory);
        }
        break;
      default:
        break;
    }
  }

  bool _hasRequiredKeys(Player player) {
    return maze.requiredKeys.every((keyId) => player.hasKey(keyId));
  }

  void addToLog(String message) {
    final timestamp = DateTime.now().toIso8601String();
    gameLog.add('[$timestamp] $message');
  }

  @override
  String toString() {
    return 'GameState(id: $id, roomCode: $roomCode, status: $status, players: ${players.length})';
  }
}
