import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/models.dart';

class WalkerView extends StatefulWidget {
  final Maze maze;
  final Player? walker;
  final Function(int deltaX, int deltaY)? onMove;
  final Function(int x, int y)? onInteract;
  final int viewRadius;

  const WalkerView({
    super.key,
    required this.maze,
    this.walker,
    this.onMove,
    this.onInteract,
    this.viewRadius = 1, // How many tiles around the player are visible
  });

  @override
  State<WalkerView> createState() => _WalkerViewState();
}

class _WalkerViewState extends State<WalkerView>
    with TickerProviderStateMixin {
  late AnimationController _moveController;
  late AnimationController _interactionController;
  
  String? _lastAction;
  bool _isMoving = false;

  @override
  void initState() {
    super.initState();
    _moveController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _interactionController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _moveController.dispose();
    _interactionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.green.shade50,
            Colors.green.shade100,
          ],
        ),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: Column(
              children: [
                Expanded(
                  flex: 2,
                  child: _buildLimitedView(),
                ),
                Expanded(
                  flex: 1,
                  child: _buildInventoryAndStatus(),
                ),
              ],
            ),
          ),
          _buildControlPanel(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.shade600,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          const Icon(
            Icons.directions_walk,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 8),
          const Text(
            'Walker View',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          if (widget.walker != null) ...[
            const Icon(
              Icons.favorite,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 4),
            Text(
              '${widget.walker!.lives}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLimitedView() {
    if (widget.walker == null) {
      return const Center(
        child: Text('Waiting for player...'),
      );
    }

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.shade300, width: 2),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: _buildVisibleArea(),
      ),
    );
  }

  Widget _buildVisibleArea() {
    final walker = widget.walker!;
    final viewSize = (widget.viewRadius * 2) + 1;
    
    return GridView.builder(
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: viewSize,
        childAspectRatio: 1.0,
      ),
      itemCount: viewSize * viewSize,
      itemBuilder: (context, index) {
        final localX = index % viewSize;
        final localY = index ~/ viewSize;
        final worldX = walker.x - widget.viewRadius + localX;
        final worldY = walker.y - widget.viewRadius + localY;
        
        return _buildViewTile(worldX, worldY, localX, localY);
      },
    );
  }

  Widget _buildViewTile(int worldX, int worldY, int localX, int localY) {
    final isPlayerPosition = widget.walker?.x == worldX && widget.walker?.y == worldY;
    final isCenter = localX == widget.viewRadius && localY == widget.viewRadius;
    
    // Check if this tile is within the maze bounds
    if (!widget.maze.isValidPosition(worldX, worldY)) {
      return _buildUnknownTile();
    }

    final element = widget.maze.getElementAt(worldX, worldY);
    
    return GestureDetector(
      onTap: () {
        if (!isPlayerPosition) {
          widget.onInteract?.call(worldX, worldY);
          _triggerInteractionFeedback();
        }
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        margin: const EdgeInsets.all(1),
        decoration: BoxDecoration(
          color: _getWalkerTileColor(element, isPlayerPosition),
          border: Border.all(
            color: isCenter ? Colors.green : Colors.grey.shade600,
            width: isCenter ? 3 : 1,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Stack(
          children: [
            if (isPlayerPosition) _buildPlayerIndicator(),
            if (!isPlayerPosition) _buildTileContent(element),
            if (_canInteractWith(element, worldX, worldY)) _buildInteractionIndicator(),
          ],
        ),
      ),
    );
  }

  Widget _buildUnknownTile() {
    return Container(
      margin: const EdgeInsets.all(1),
      decoration: BoxDecoration(
        color: Colors.grey.shade800,
        borderRadius: BorderRadius.circular(4),
      ),
      child: const Center(
        child: Text(
          '?',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildPlayerIndicator() {
    return AnimatedBuilder(
      animation: _moveController,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.green.withOpacity(0.8),
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Center(
            child: Icon(
              Icons.person,
              color: Colors.white,
              size: 24,
            ),
          ),
        );
      },
    );
  }

  Widget _buildTileContent(PuzzleElement element) {
    Widget? content;
    
    switch (element.type) {
      case PuzzleElementType.wall:
        content = Container(
          decoration: BoxDecoration(
            color: Colors.grey.shade700,
            borderRadius: BorderRadius.circular(4),
          ),
        );
        break;
      case PuzzleElementType.key:
        if (element.properties['collected'] != true) {
          content = const Icon(
            Icons.key,
            color: Colors.yellow,
            size: 20,
          );
        }
        break;
      case PuzzleElementType.door:
        content = Icon(
          element.state == ElementState.unlocked
              ? Icons.door_front_door_outlined
              : Icons.lock,
          color: element.state == ElementState.unlocked
              ? Colors.green
              : Colors.brown,
          size: 20,
        );
        break;
      case PuzzleElementType.exit:
        content = const Icon(
          Icons.flag,
          color: Colors.green,
          size: 20,
        );
        break;
      default:
        content = Container();
        break;
    }

    return Center(child: content);
  }

  Widget _buildInteractionIndicator() {
    return AnimatedBuilder(
      animation: _interactionController,
      builder: (context, child) {
        return Positioned(
          top: 2,
          right: 2,
          child: Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.5 + 0.5 * _interactionController.value),
              shape: BoxShape.circle,
            ),
          ),
        );
      },
    );
  }

  Widget _buildInventoryAndStatus() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Inventory & Status',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(child: _buildInventory()),
              const SizedBox(width: 16),
              Expanded(child: _buildStatus()),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInventory() {
    final inventory = widget.walker?.inventory ?? [];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Keys',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 4),
        if (inventory.isEmpty)
          const Text(
            'No keys',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          )
        else
          Wrap(
            spacing: 4,
            children: inventory.map((keyId) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.amber.shade100,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.amber),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.key, size: 12, color: Colors.amber),
                    const SizedBox(width: 4),
                    Text(
                      keyId,
                      style: const TextStyle(fontSize: 10),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
      ],
    );
  }

  Widget _buildStatus() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Last Action',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          _lastAction ?? 'None',
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildControlPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildMovementControls(),
          const SizedBox(height: 16),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildMovementControls() {
    return Column(
      children: [
        // Up button
        _buildMoveButton(Icons.keyboard_arrow_up, 'Up', () => _move(0, -1)),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Left button
            _buildMoveButton(Icons.keyboard_arrow_left, 'Left', () => _move(-1, 0)),
            const SizedBox(width: 16),
            // Center (interact) button
            _buildInteractButton(),
            const SizedBox(width: 16),
            // Right button
            _buildMoveButton(Icons.keyboard_arrow_right, 'Right', () => _move(1, 0)),
          ],
        ),
        const SizedBox(height: 8),
        // Down button
        _buildMoveButton(Icons.keyboard_arrow_down, 'Down', () => _move(0, 1)),
      ],
    );
  }

  Widget _buildMoveButton(IconData icon, String direction, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: Colors.green.shade100,
          borderRadius: BorderRadius.circular(30),
          border: Border.all(color: Colors.green.shade300, width: 2),
        ),
        child: Icon(
          icon,
          color: Colors.green.shade700,
          size: 30,
        ),
      ),
    );
  }

  Widget _buildInteractButton() {
    return GestureDetector(
      onTap: _interact,
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: Colors.orange.shade100,
          borderRadius: BorderRadius.circular(30),
          border: Border.all(color: Colors.orange.shade300, width: 2),
        ),
        child: Icon(
          Icons.touch_app,
          color: Colors.orange.shade700,
          size: 30,
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildActionButton(
          Icons.help_outline,
          'Help',
          () => _showHelp(),
        ),
        _buildActionButton(
          Icons.refresh,
          'Reset View',
          () => _resetView(),
        ),
      ],
    );
  }

  Widget _buildActionButton(IconData icon, String label, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16, color: Colors.grey.shade700),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade700,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  Color _getWalkerTileColor(PuzzleElement element, bool isPlayerPosition) {
    if (isPlayerPosition) {
      return Colors.green.withOpacity(0.3);
    }

    switch (element.type) {
      case PuzzleElementType.wall:
        return Colors.grey.shade700;
      case PuzzleElementType.empty:
        return Colors.grey.shade200;
      case PuzzleElementType.key:
        return element.properties['collected'] == true
            ? Colors.grey.shade200
            : Colors.yellow.withOpacity(0.3);
      case PuzzleElementType.door:
        return element.state == ElementState.unlocked
            ? Colors.green.withOpacity(0.3)
            : Colors.brown.withOpacity(0.3);
      case PuzzleElementType.exit:
        return Colors.green.withOpacity(0.4);
      default:
        return Colors.grey.shade200;
    }
  }

  bool _canInteractWith(PuzzleElement element, int x, int y) {
    if (widget.walker == null) return false;
    
    final distance = (widget.walker!.x - x).abs() + (widget.walker!.y - y).abs();
    return distance <= 1 && element.isInteractable;
  }

  void _move(int deltaX, int deltaY) {
    if (_isMoving) return;
    
    _isMoving = true;
    _moveController.forward().then((_) {
      _moveController.reverse();
      _isMoving = false;
    });

    widget.onMove?.call(deltaX, deltaY);
    
    final directions = ['up', 'down', 'left', 'right'];
    if (deltaY == -1) _lastAction = 'Moved up';
    else if (deltaY == 1) _lastAction = 'Moved down';
    else if (deltaX == -1) _lastAction = 'Moved left';
    else if (deltaX == 1) _lastAction = 'Moved right';
    
    setState(() {});
    
    // Haptic feedback
    HapticFeedback.lightImpact();
  }

  void _interact() {
    if (widget.walker == null) return;
    
    widget.onInteract?.call(widget.walker!.x, widget.walker!.y);
    _triggerInteractionFeedback();
    _lastAction = 'Interacted';
    setState(() {});
    
    HapticFeedback.mediumImpact();
  }

  void _triggerInteractionFeedback() {
    _interactionController.forward().then((_) {
      _interactionController.reverse();
    });
  }

  void _showHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Walker Controls'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('• Use arrow buttons to move'),
            Text('• Tap center button to interact'),
            Text('• Collect keys to unlock doors'),
            Text('• Avoid traps (red warning icons)'),
            Text('• Reach the exit flag to win'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  void _resetView() {
    setState(() {
      _lastAction = 'View reset';
    });
  }
}
