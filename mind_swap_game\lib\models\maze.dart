import 'dart:math';
import 'package:uuid/uuid.dart';
import 'puzzle_element.dart';

class Position {
  final int x;
  final int y;

  const Position(this.x, this.y);

  Position copyWith({int? x, int? y}) {
    return Position(x ?? this.x, y ?? this.y);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Position && other.x == x && other.y == y;
  }

  @override
  int get hashCode => x.hashCode ^ y.hashCode;

  @override
  String toString() => 'Position($x, $y)';

  Map<String, dynamic> toJson() => {'x': x, 'y': y};
  
  factory Position.fromJson(Map<String, dynamic> json) {
    return Position(json['x'], json['y']);
  }
}

enum MazeDifficulty {
  easy,    // 6x6, simple puzzles
  medium,  // 8x8, moderate puzzles
  hard,    // 10x10, complex puzzles
}

class Maze {
  final String id;
  final int width;
  final int height;
  final MazeDifficulty difficulty;
  final List<List<PuzzleElement>> grid;
  final Position startPosition;
  final Position exitPosition;
  final Map<String, PuzzleElement> elements;
  final List<String> requiredKeys;
  final int timeLimit; // in seconds

  Maze({
    String? id,
    required this.width,
    required this.height,
    required this.difficulty,
    required this.grid,
    required this.startPosition,
    required this.exitPosition,
    Map<String, PuzzleElement>? elements,
    List<String>? requiredKeys,
    int? timeLimit,
  }) : id = id ?? const Uuid().v4(),
       elements = elements ?? {},
       requiredKeys = requiredKeys ?? [],
       timeLimit = timeLimit ?? _getDefaultTimeLimit(difficulty);

  static int _getDefaultTimeLimit(MazeDifficulty difficulty) {
    switch (difficulty) {
      case MazeDifficulty.easy:
        return 300; // 5 minutes
      case MazeDifficulty.medium:
        return 480; // 8 minutes
      case MazeDifficulty.hard:
        return 600; // 10 minutes
    }
  }

  Maze copyWith({
    String? id,
    int? width,
    int? height,
    MazeDifficulty? difficulty,
    List<List<PuzzleElement>>? grid,
    Position? startPosition,
    Position? exitPosition,
    Map<String, PuzzleElement>? elements,
    List<String>? requiredKeys,
    int? timeLimit,
  }) {
    return Maze(
      id: id ?? this.id,
      width: width ?? this.width,
      height: height ?? this.height,
      difficulty: difficulty ?? this.difficulty,
      grid: grid ?? this.grid.map((row) => List.from(row)).toList(),
      startPosition: startPosition ?? this.startPosition,
      exitPosition: exitPosition ?? this.exitPosition,
      elements: elements ?? Map.from(this.elements),
      requiredKeys: requiredKeys ?? List.from(this.requiredKeys),
      timeLimit: timeLimit ?? this.timeLimit,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'width': width,
      'height': height,
      'difficulty': difficulty.name,
      'grid': grid.map((row) => row.map((element) => element.toJson()).toList()).toList(),
      'startPosition': startPosition.toJson(),
      'exitPosition': exitPosition.toJson(),
      'elements': elements.map((key, value) => MapEntry(key, value.toJson())),
      'requiredKeys': requiredKeys,
      'timeLimit': timeLimit,
    };
  }

  factory Maze.fromJson(Map<String, dynamic> json) {
    final gridData = json['grid'] as List;
    final grid = gridData.map((row) => 
      (row as List).map((elementData) => 
        PuzzleElement.fromJson(elementData)
      ).toList()
    ).toList();

    final elementsData = json['elements'] as Map<String, dynamic>;
    final elements = elementsData.map((key, value) => 
      MapEntry(key, PuzzleElement.fromJson(value))
    );

    return Maze(
      id: json['id'],
      width: json['width'],
      height: json['height'],
      difficulty: MazeDifficulty.values.firstWhere((e) => e.name == json['difficulty']),
      grid: grid,
      startPosition: Position.fromJson(json['startPosition']),
      exitPosition: Position.fromJson(json['exitPosition']),
      elements: elements,
      requiredKeys: List<String>.from(json['requiredKeys'] ?? []),
      timeLimit: json['timeLimit'],
    );
  }

  // Factory constructor for generating a random maze
  factory Maze.generate({
    MazeDifficulty difficulty = MazeDifficulty.medium,
    Random? random,
  }) {
    random ??= Random();
    
    final dimensions = _getDimensions(difficulty);
    final width = dimensions['width']!;
    final height = dimensions['height']!;
    
    // Create empty grid
    final grid = List.generate(
      height,
      (y) => List.generate(
        width,
        (x) => PuzzleElement.empty(x: x, y: y),
      ),
    );

    // Generate maze layout
    final maze = Maze(
      width: width,
      height: height,
      difficulty: difficulty,
      grid: grid,
      startPosition: const Position(0, 0),
      exitPosition: Position(width - 1, height - 1),
    );

    return maze._generateLayout(random);
  }

  static Map<String, int> _getDimensions(MazeDifficulty difficulty) {
    switch (difficulty) {
      case MazeDifficulty.easy:
        return {'width': 6, 'height': 6};
      case MazeDifficulty.medium:
        return {'width': 8, 'height': 8};
      case MazeDifficulty.hard:
        return {'width': 10, 'height': 10};
    }
  }

  Maze _generateLayout(Random random) {
    // This is a simplified maze generation
    // In a real implementation, you'd use algorithms like recursive backtracking
    
    final newGrid = List.generate(
      height,
      (y) => List.generate(
        width,
        (x) => PuzzleElement.empty(x: x, y: y),
      ),
    );

    final newElements = <String, PuzzleElement>{};
    final newRequiredKeys = <String>[];

    // Add walls around the perimeter
    for (int x = 0; x < width; x++) {
      newGrid[0][x] = PuzzleElement.wall(x: x, y: 0);
      newGrid[height - 1][x] = PuzzleElement.wall(x: x, y: height - 1);
    }
    for (int y = 0; y < height; y++) {
      newGrid[y][0] = PuzzleElement.wall(x: 0, y: y);
      newGrid[y][width - 1] = PuzzleElement.wall(x: width - 1, y: y);
    }

    // Set start and exit positions
    newGrid[1][1] = PuzzleElement.empty(x: 1, y: 1);
    newGrid[height - 2][width - 2] = PuzzleElement.exit(x: width - 2, y: height - 2);

    // Add some random walls
    final wallCount = (width * height * 0.2).round();
    for (int i = 0; i < wallCount; i++) {
      final x = random.nextInt(width - 2) + 1;
      final y = random.nextInt(height - 2) + 1;
      
      if (x != 1 || y != 1) { // Don't block start
        if (x != width - 2 || y != height - 2) { // Don't block exit
          newGrid[y][x] = PuzzleElement.wall(x: x, y: y);
        }
      }
    }

    // Add puzzle elements based on difficulty
    _addPuzzleElements(newGrid, newElements, newRequiredKeys, random);

    return copyWith(
      grid: newGrid,
      startPosition: const Position(1, 1),
      exitPosition: Position(width - 2, height - 2),
      elements: newElements,
      requiredKeys: newRequiredKeys,
    );
  }

  void _addPuzzleElements(
    List<List<PuzzleElement>> grid,
    Map<String, PuzzleElement> elements,
    List<String> requiredKeys,
    Random random,
  ) {
    final elementCount = _getElementCount();
    
    // Add keys and doors
    for (int i = 0; i < elementCount['keys']!; i++) {
      final keyId = 'key_$i';
      final keyPos = _findEmptyPosition(grid, random);
      final doorPos = _findEmptyPosition(grid, random);
      
      if (keyPos != null && doorPos != null) {
        final key = PuzzleElement.key(x: keyPos.x, y: keyPos.y, keyId: keyId);
        final door = PuzzleElement.door(x: doorPos.x, y: doorPos.y, requiredKeyId: keyId);
        
        grid[keyPos.y][keyPos.x] = key;
        grid[doorPos.y][doorPos.x] = door;
        elements[key.id] = key;
        elements[door.id] = door;
        requiredKeys.add(keyId);
      }
    }

    // Add traps
    for (int i = 0; i < elementCount['traps']!; i++) {
      final pos = _findEmptyPosition(grid, random);
      if (pos != null) {
        final trap = PuzzleElement.trap(x: pos.x, y: pos.y);
        grid[pos.y][pos.x] = trap;
        elements[trap.id] = trap;
      }
    }

    // Add pressure plates
    for (int i = 0; i < elementCount['plates']!; i++) {
      final pos = _findEmptyPosition(grid, random);
      if (pos != null) {
        final plate = PuzzleElement.pressurePlate(
          x: pos.x, 
          y: pos.y, 
          triggerId: 'trigger_$i'
        );
        grid[pos.y][pos.x] = plate;
        elements[plate.id] = plate;
      }
    }
  }

  Map<String, int> _getElementCount() {
    switch (difficulty) {
      case MazeDifficulty.easy:
        return {'keys': 1, 'traps': 2, 'plates': 1};
      case MazeDifficulty.medium:
        return {'keys': 2, 'traps': 4, 'plates': 2};
      case MazeDifficulty.hard:
        return {'keys': 3, 'traps': 6, 'plates': 3};
    }
  }

  Position? _findEmptyPosition(List<List<PuzzleElement>> grid, Random random) {
    final attempts = 50;
    for (int i = 0; i < attempts; i++) {
      final x = random.nextInt(width - 2) + 1;
      final y = random.nextInt(height - 2) + 1;
      
      if (grid[y][x].type == PuzzleElementType.empty) {
        return Position(x, y);
      }
    }
    return null;
  }

  // Helper methods
  PuzzleElement getElementAt(int x, int y) {
    if (x < 0 || x >= width || y < 0 || y >= height) {
      return PuzzleElement.wall(x: x, y: y);
    }
    return grid[y][x];
  }

  void setElementAt(int x, int y, PuzzleElement element) {
    if (x >= 0 && x < width && y >= 0 && y < height) {
      grid[y][x] = element;
      elements[element.id] = element;
    }
  }

  bool isValidPosition(int x, int y) {
    return x >= 0 && x < width && y >= 0 && y < height;
  }

  bool isWalkable(int x, int y) {
    if (!isValidPosition(x, y)) return false;
    return getElementAt(x, y).isWalkable;
  }

  List<PuzzleElement> getElementsOfType(PuzzleElementType type) {
    return elements.values.where((element) => element.type == type).toList();
  }

  @override
  String toString() {
    return 'Maze(id: $id, size: ${width}x$height, difficulty: $difficulty)';
  }
}
