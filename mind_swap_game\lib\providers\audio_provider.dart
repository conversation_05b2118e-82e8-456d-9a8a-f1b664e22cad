import 'package:flutter/foundation.dart';
import 'package:audioplayers/audioplayers.dart';

enum SoundEffect {
  move,
  keyCollect,
  doorUnlock,
  trapTrigger,
  roleSwap,
  gameStart,
  gameWin,
  gameLose,
  buttonPress,
  warning,
  pressurePlate,
  mirrorTile,
}

class AudioProvider extends ChangeNotifier {
  final AudioPlayer _sfxPlayer = AudioPlayer();
  final AudioPlayer _musicPlayer = AudioPlayer();
  
  bool _soundEnabled = true;
  bool _musicEnabled = true;
  double _soundVolume = 0.7;
  double _musicVolume = 0.5;

  // Getters
  bool get soundEnabled => _soundEnabled;
  bool get musicEnabled => _musicEnabled;
  double get soundVolume => _soundVolume;
  double get musicVolume => _musicVolume;

  // Sound effect file paths (these would be actual audio files in assets)
  final Map<SoundEffect, String> _soundPaths = {
    SoundEffect.move: 'sounds/move.wav',
    SoundEffect.keyCollect: 'sounds/key_collect.wav',
    SoundEffect.doorUnlock: 'sounds/door_unlock.wav',
    SoundEffect.trapTrigger: 'sounds/trap_trigger.wav',
    SoundEffect.roleSwap: 'sounds/role_swap.wav',
    SoundEffect.gameStart: 'sounds/game_start.wav',
    SoundEffect.gameWin: 'sounds/game_win.wav',
    SoundEffect.gameLose: 'sounds/game_lose.wav',
    SoundEffect.buttonPress: 'sounds/button_press.wav',
    SoundEffect.warning: 'sounds/warning.wav',
    SoundEffect.pressurePlate: 'sounds/pressure_plate.wav',
    SoundEffect.mirrorTile: 'sounds/mirror_tile.wav',
  };

  AudioProvider() {
    _initializeAudio();
  }

  void _initializeAudio() {
    _sfxPlayer.setVolume(_soundVolume);
    _musicPlayer.setVolume(_musicVolume);
  }

  // Sound effect methods
  Future<void> playSound(SoundEffect effect) async {
    if (!_soundEnabled) return;

    try {
      final path = _soundPaths[effect];
      if (path != null) {
        // For now, we'll use a placeholder sound or generate programmatic sounds
        // In a real app, you would load actual audio files from assets
        await _playProgrammaticSound(effect);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error playing sound effect $effect: $e');
      }
    }
  }

  // Programmatic sound generation for demo purposes
  Future<void> _playProgrammaticSound(SoundEffect effect) async {
    // This is a placeholder implementation
    // In a real app, you would play actual audio files
    switch (effect) {
      case SoundEffect.move:
        // Short, subtle movement sound
        break;
      case SoundEffect.keyCollect:
        // Pleasant collection sound
        break;
      case SoundEffect.doorUnlock:
        // Mechanical unlocking sound
        break;
      case SoundEffect.trapTrigger:
        // Alarming trap sound
        break;
      case SoundEffect.roleSwap:
        // Distinctive role swap sound
        break;
      case SoundEffect.gameStart:
        // Upbeat game start sound
        break;
      case SoundEffect.gameWin:
        // Victory fanfare
        break;
      case SoundEffect.gameLose:
        // Defeat sound
        break;
      case SoundEffect.buttonPress:
        // UI button sound
        break;
      case SoundEffect.warning:
        // Warning beep
        break;
      case SoundEffect.pressurePlate:
        // Mechanical activation sound
        break;
      case SoundEffect.mirrorTile:
        // Mystical mirror sound
        break;
    }
  }

  // Background music methods
  Future<void> playBackgroundMusic() async {
    if (!_musicEnabled) return;

    try {
      // In a real app, you would play an actual music file
      // await _musicPlayer.play(AssetSource('music/background.mp3'));
      // await _musicPlayer.setReleaseMode(ReleaseMode.loop);
    } catch (e) {
      if (kDebugMode) {
        print('Error playing background music: $e');
      }
    }
  }

  Future<void> stopBackgroundMusic() async {
    try {
      await _musicPlayer.stop();
    } catch (e) {
      if (kDebugMode) {
        print('Error stopping background music: $e');
      }
    }
  }

  Future<void> pauseBackgroundMusic() async {
    try {
      await _musicPlayer.pause();
    } catch (e) {
      if (kDebugMode) {
        print('Error pausing background music: $e');
      }
    }
  }

  Future<void> resumeBackgroundMusic() async {
    try {
      await _musicPlayer.resume();
    } catch (e) {
      if (kDebugMode) {
        print('Error resuming background music: $e');
      }
    }
  }

  // Settings methods
  void setSoundEnabled(bool enabled) {
    _soundEnabled = enabled;
    notifyListeners();
  }

  void setMusicEnabled(bool enabled) {
    _musicEnabled = enabled;
    if (!enabled) {
      stopBackgroundMusic();
    } else {
      playBackgroundMusic();
    }
    notifyListeners();
  }

  void setSoundVolume(double volume) {
    _soundVolume = volume.clamp(0.0, 1.0);
    _sfxPlayer.setVolume(_soundVolume);
    notifyListeners();
  }

  void setMusicVolume(double volume) {
    _musicVolume = volume.clamp(0.0, 1.0);
    _musicPlayer.setVolume(_musicVolume);
    notifyListeners();
  }

  // Game-specific audio cues
  void playMoveSound() => playSound(SoundEffect.move);
  void playKeyCollectSound() => playSound(SoundEffect.keyCollect);
  void playDoorUnlockSound() => playSound(SoundEffect.doorUnlock);
  void playTrapTriggerSound() => playSound(SoundEffect.trapTrigger);
  void playRoleSwapSound() => playSound(SoundEffect.roleSwap);
  void playGameStartSound() => playSound(SoundEffect.gameStart);
  void playGameWinSound() => playSound(SoundEffect.gameWin);
  void playGameLoseSound() => playSound(SoundEffect.gameLose);
  void playButtonPressSound() => playSound(SoundEffect.buttonPress);
  void playWarningSound() => playSound(SoundEffect.warning);
  void playPressurePlateSound() => playSound(SoundEffect.pressurePlate);
  void playMirrorTileSound() => playSound(SoundEffect.mirrorTile);

  // Audio feedback for role swap warning
  void playRoleSwapWarning() {
    playWarningSound();
  }

  // Audio feedback for game events
  void onPlayerMove() {
    playMoveSound();
  }

  void onKeyCollected() {
    playKeyCollectSound();
  }

  void onDoorUnlocked() {
    playDoorUnlockSound();
  }

  void onTrapTriggered() {
    playTrapTriggerSound();
  }

  void onRoleSwap() {
    playRoleSwapSound();
  }

  void onGameStart() {
    playGameStartSound();
    playBackgroundMusic();
  }

  void onGameWin() {
    stopBackgroundMusic();
    playGameWinSound();
  }

  void onGameLose() {
    stopBackgroundMusic();
    playGameLoseSound();
  }

  void onPressurePlateActivated() {
    playPressurePlateSound();
  }

  void onMirrorTileActivated() {
    playMirrorTileSound();
  }

  // Cleanup
  @override
  void dispose() {
    _sfxPlayer.dispose();
    _musicPlayer.dispose();
    super.dispose();
  }
}
