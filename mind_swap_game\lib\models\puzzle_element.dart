import 'package:uuid/uuid.dart';

enum PuzzleElementType {
  empty,
  wall,
  key,
  door,
  trap,
  pressurePlate,
  mirrorTile,
  fogZone,
  exit,
  startPosition,
}

enum ElementState {
  inactive,
  active,
  triggered,
  unlocked,
  locked,
}

class PuzzleElement {
  final String id;
  final PuzzleElementType type;
  final int x;
  final int y;
  ElementState state;
  Map<String, dynamic> properties;

  PuzzleElement({
    String? id,
    required this.type,
    required this.x,
    required this.y,
    this.state = ElementState.inactive,
    Map<String, dynamic>? properties,
  }) : id = id ?? const Uuid().v4(),
       properties = properties ?? {};

  PuzzleElement copyWith({
    String? id,
    PuzzleElementType? type,
    int? x,
    int? y,
    ElementState? state,
    Map<String, dynamic>? properties,
  }) {
    return PuzzleElement(
      id: id ?? this.id,
      type: type ?? this.type,
      x: x ?? this.x,
      y: y ?? this.y,
      state: state ?? this.state,
      properties: properties ?? Map.from(this.properties),
    );
  }

  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      'id': id,
      'type': type.name,
      'x': x,
      'y': y,
      'state': state.name,
      'properties': properties,
    };
  }

  factory PuzzleElement.fromJson(Map<String, dynamic> json) {
    return PuzzleElement(
      id: json['id'],
      type: PuzzleElementType.values.firstWhere((e) => e.name == json['type']),
      x: json['x'],
      y: json['y'],
      state: ElementState.values.firstWhere((e) => e.name == json['state']),
      properties: Map<String, dynamic>.from(json['properties'] ?? {}),
    );
  }

  // Factory constructors for specific element types
  factory PuzzleElement.key({
    required int x,
    required int y,
    required String keyId,
    String? color,
  }) {
    return PuzzleElement(
      type: PuzzleElementType.key,
      x: x,
      y: y,
      state: ElementState.active,
      properties: {
        'keyId': keyId,
        'color': color ?? 'gold',
        'collected': false,
      },
    );
  }

  factory PuzzleElement.door({
    required int x,
    required int y,
    required String requiredKeyId,
    String? color,
  }) {
    return PuzzleElement(
      type: PuzzleElementType.door,
      x: x,
      y: y,
      state: ElementState.locked,
      properties: {
        'requiredKeyId': requiredKeyId,
        'color': color ?? 'brown',
      },
    );
  }

  factory PuzzleElement.trap({
    required int x,
    required int y,
    bool isVisible = false,
  }) {
    return PuzzleElement(
      type: PuzzleElementType.trap,
      x: x,
      y: y,
      state: ElementState.active,
      properties: {
        'isVisible': isVisible,
        'triggered': false,
      },
    );
  }

  factory PuzzleElement.pressurePlate({
    required int x,
    required int y,
    required String triggerId,
  }) {
    return PuzzleElement(
      type: PuzzleElementType.pressurePlate,
      x: x,
      y: y,
      state: ElementState.inactive,
      properties: {
        'triggerId': triggerId,
        'activated': false,
      },
    );
  }

  factory PuzzleElement.mirrorTile({
    required int x,
    required int y,
    int duration = 5,
  }) {
    return PuzzleElement(
      type: PuzzleElementType.mirrorTile,
      x: x,
      y: y,
      state: ElementState.active,
      properties: {
        'duration': duration,
        'triggered': false,
      },
    );
  }

  factory PuzzleElement.fogZone({
    required int x,
    required int y,
    int radius = 1,
  }) {
    return PuzzleElement(
      type: PuzzleElementType.fogZone,
      x: x,
      y: y,
      state: ElementState.active,
      properties: {
        'radius': radius,
      },
    );
  }

  factory PuzzleElement.wall({
    required int x,
    required int y,
  }) {
    return PuzzleElement(
      type: PuzzleElementType.wall,
      x: x,
      y: y,
      state: ElementState.active,
    );
  }

  factory PuzzleElement.empty({
    required int x,
    required int y,
  }) {
    return PuzzleElement(
      type: PuzzleElementType.empty,
      x: x,
      y: y,
      state: ElementState.inactive,
    );
  }

  factory PuzzleElement.exit({
    required int x,
    required int y,
  }) {
    return PuzzleElement(
      type: PuzzleElementType.exit,
      x: x,
      y: y,
      state: ElementState.active,
    );
  }

  // Helper methods
  bool get isWalkable {
    switch (type) {
      case PuzzleElementType.wall:
        return false;
      case PuzzleElementType.door:
        return state == ElementState.unlocked;
      default:
        return true;
    }
  }

  bool get isCollectable {
    return type == PuzzleElementType.key && 
           properties['collected'] == false;
  }

  bool get isInteractable {
    switch (type) {
      case PuzzleElementType.key:
      case PuzzleElementType.door:
      case PuzzleElementType.pressurePlate:
        return true;
      default:
        return false;
    }
  }

  void activate() {
    state = ElementState.active;
    if (type == PuzzleElementType.pressurePlate) {
      properties['activated'] = true;
    }
  }

  void deactivate() {
    state = ElementState.inactive;
    if (type == PuzzleElementType.pressurePlate) {
      properties['activated'] = false;
    }
  }

  void unlock() {
    if (type == PuzzleElementType.door) {
      state = ElementState.unlocked;
    }
  }

  void collect() {
    if (type == PuzzleElementType.key) {
      properties['collected'] = true;
      state = ElementState.triggered;
    }
  }

  void trigger() {
    state = ElementState.triggered;
    if (type == PuzzleElementType.trap) {
      properties['triggered'] = true;
    } else if (type == PuzzleElementType.mirrorTile) {
      properties['triggered'] = true;
    }
  }

  @override
  String toString() {
    return 'PuzzleElement(id: $id, type: $type, position: ($x, $y), state: $state)';
  }
}
