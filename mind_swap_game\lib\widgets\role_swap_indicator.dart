import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../services/role_swap_service.dart';
import '../models/models.dart';

class RoleSwapIndicator extends StatefulWidget {
  final RoleSwapService roleSwapService;
  final PlayerRole? currentRole;
  final VoidCallback? onSwapComplete;

  const RoleSwapIndicator({
    super.key,
    required this.roleSwapService,
    this.currentRole,
    this.onSwapComplete,
  });

  @override
  State<RoleSwapIndicator> createState() => _RoleSwapIndicatorState();
}

class _RoleSwapIndicatorState extends State<RoleSwapIndicator>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _scaleController;
  late AnimationController _rotationController;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Listen to role swap events
    widget.roleSwapService.eventStream.listen(_onRoleSwapEvent);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _scaleController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  void _onRoleSwapEvent(RoleSwapEvent event) {
    switch (event.phase) {
      case SwapPhase.warning:
        _pulseController.repeat(reverse: true);
        break;
      case SwapPhase.countdown:
        _scaleController.forward().then((_) => _scaleController.reverse());
        break;
      case SwapPhase.swapping:
        _rotationController.forward();
        break;
      case SwapPhase.complete:
        _rotationController.reset();
        _pulseController.stop();
        _scaleController.reset();
        widget.onSwapComplete?.call();
        break;
      case SwapPhase.normal:
        _pulseController.stop();
        _scaleController.reset();
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<RoleSwapEvent>(
      stream: widget.roleSwapService.eventStream,
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const SizedBox.shrink();
        }

        final event = snapshot.data!;
        return _buildIndicator(event);
      },
    );
  }

  Widget _buildIndicator(RoleSwapEvent event) {
    switch (event.phase) {
      case SwapPhase.normal:
        return _buildNormalIndicator(event);
      case SwapPhase.warning:
        return _buildWarningIndicator(event);
      case SwapPhase.countdown:
        return _buildCountdownIndicator(event);
      case SwapPhase.swapping:
        return _buildSwappingIndicator(event);
      case SwapPhase.complete:
        return _buildCompleteIndicator(event);
    }
  }

  Widget _buildNormalIndicator(RoleSwapEvent event) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getRoleIcon(widget.currentRole),
            color: Colors.blue,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            'Next swap: ${_formatTime(event.timeRemaining)}',
            style: const TextStyle(
              color: Colors.blue,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWarningIndicator(RoleSwapEvent event) {
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.orange.withOpacity(0.2 + 0.1 * _pulseController.value),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.orange.withOpacity(0.5 + 0.3 * _pulseController.value),
              width: 2,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.warning,
                color: Colors.orange,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Swap in ${event.timeRemaining}s!',
                style: const TextStyle(
                  color: Colors.orange,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCountdownIndicator(RoleSwapEvent event) {
    return AnimatedBuilder(
      animation: _scaleController,
      builder: (context, child) {
        final scale = 1.0 + 0.3 * _scaleController.value;
        return Transform.scale(
          scale: scale,
          child: Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.9),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.red.withOpacity(0.5),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Center(
              child: Text(
                event.timeRemaining.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSwappingIndicator(RoleSwapEvent event) {
    return AnimatedBuilder(
      animation: _rotationController,
      builder: (context, child) {
        return Transform.rotate(
          angle: _rotationController.value * 2 * 3.14159,
          child: Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.purple, Colors.blue],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.purple.withOpacity(0.5),
                  blurRadius: 25,
                  spreadRadius: 10,
                ),
              ],
            ),
            child: const Center(
              child: Icon(
                Icons.swap_horiz,
                color: Colors.white,
                size: 40,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCompleteIndicator(RoleSwapEvent event) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.green.withOpacity(0.5)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getRoleIcon(widget.currentRole),
            color: Colors.green,
            size: 20,
          ),
          const SizedBox(width: 8),
          const Text(
            'Roles swapped!',
            style: TextStyle(
              color: Colors.green,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    ).animate().scale(
      duration: const Duration(milliseconds: 300),
      curve: Curves.elasticOut,
    );
  }

  IconData _getRoleIcon(PlayerRole? role) {
    switch (role) {
      case PlayerRole.navigator:
        return Icons.map;
      case PlayerRole.walker:
        return Icons.directions_walk;
      default:
        return Icons.person;
    }
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }
}

class RoleSwapOverlay extends StatelessWidget {
  final RoleSwapService roleSwapService;
  final PlayerRole? currentRole;
  final PlayerRole? newRole;

  const RoleSwapOverlay({
    super.key,
    required this.roleSwapService,
    this.currentRole,
    this.newRole,
  });

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<RoleSwapEvent>(
      stream: roleSwapService.eventStream,
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const SizedBox.shrink();
        }

        final event = snapshot.data!;
        
        if (event.phase != SwapPhase.swapping && event.phase != SwapPhase.complete) {
          return const SizedBox.shrink();
        }

        return Container(
          color: Colors.black.withOpacity(0.8),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.swap_horiz,
                  color: Colors.white,
                  size: 80,
                ).animate().rotate(
                  duration: const Duration(milliseconds: 1500),
                  curve: Curves.elasticOut,
                ),
                const SizedBox(height: 20),
                Text(
                  'ROLES SWAPPED!',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ).animate().fadeIn(
                  duration: const Duration(milliseconds: 500),
                ).slideY(
                  begin: 0.3,
                  duration: const Duration(milliseconds: 500),
                ),
                const SizedBox(height: 20),
                if (currentRole != null && newRole != null) ...[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildRoleCard(currentRole!, 'Was'),
                      const SizedBox(width: 20),
                      const Icon(
                        Icons.arrow_forward,
                        color: Colors.white,
                        size: 30,
                      ),
                      const SizedBox(width: 20),
                      _buildRoleCard(newRole!, 'Now'),
                    ],
                  ).animate().fadeIn(
                    delay: const Duration(milliseconds: 500),
                    duration: const Duration(milliseconds: 500),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildRoleCard(PlayerRole role, String label) {
    final isNavigator = role == PlayerRole.navigator;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isNavigator ? Colors.blue.withOpacity(0.2) : Colors.green.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isNavigator ? Colors.blue : Colors.green,
          width: 2,
        ),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 8),
          Icon(
            isNavigator ? Icons.map : Icons.directions_walk,
            color: isNavigator ? Colors.blue : Colors.green,
            size: 30,
          ),
          const SizedBox(height: 8),
          Text(
            isNavigator ? 'Navigator' : 'Walker',
            style: TextStyle(
              color: isNavigator ? Colors.blue : Colors.green,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
