import '../models/models.dart';

enum InteractionResult {
  success,
  blocked,
  invalid,
  requiresKey,
  trapTriggered,
  keyCollected,
  doorUnlocked,
  pressurePlateActivated,
  mirrorTileActivated,
  exitReached,
}

class InteractionResponse {
  final InteractionResult result;
  final String message;
  final Map<String, dynamic> data;

  const InteractionResponse({
    required this.result,
    required this.message,
    this.data = const {},
  });

  bool get isSuccess => result == InteractionResult.success;
  bool get isBlocked => result == InteractionResult.blocked;
  bool get isInvalid => result == InteractionResult.invalid;
  bool get isTrapTriggered => result == InteractionResult.trapTriggered;
  bool get isKeyCollected => result == InteractionResult.keyCollected;
  bool get isDoorUnlocked => result == InteractionResult.doorUnlocked;
  bool get isExitReached => result == InteractionResult.exitReached;
}

class GameInteractionService {
  static InteractionResponse validateMovement(
    Maze maze,
    Player player,
    int newX,
    int newY,
  ) {
    // Check bounds
    if (!maze.isValidPosition(newX, newY)) {
      return const InteractionResponse(
        result: InteractionResult.invalid,
        message: 'Cannot move outside the maze',
      );
    }

    // Check if tile is walkable
    if (!maze.isWalkable(newX, newY)) {
      final element = maze.getElementAt(newX, newY);
      
      if (element.type == PuzzleElementType.wall) {
        return const InteractionResponse(
          result: InteractionResult.blocked,
          message: 'Cannot walk through walls',
        );
      } else if (element.type == PuzzleElementType.door) {
        final requiredKeyId = element.properties['requiredKeyId'] as String;
        return InteractionResponse(
          result: InteractionResult.requiresKey,
          message: 'Door requires key: $requiredKeyId',
          data: {'requiredKey': requiredKeyId},
        );
      }
      
      return const InteractionResponse(
        result: InteractionResult.blocked,
        message: 'Path is blocked',
      );
    }

    return const InteractionResponse(
      result: InteractionResult.success,
      message: 'Movement allowed',
    );
  }

  static InteractionResponse processMovement(
    GameState gameState,
    String playerId,
    int newX,
    int newY,
  ) {
    final player = gameState.getPlayerById(playerId);
    if (player == null) {
      return const InteractionResponse(
        result: InteractionResult.invalid,
        message: 'Player not found',
      );
    }

    // Only walker can move
    if (player.role != PlayerRole.walker) {
      return const InteractionResponse(
        result: InteractionResult.invalid,
        message: 'Only walker can move',
      );
    }

    // Validate movement
    final validationResult = validateMovement(gameState.maze, player, newX, newY);
    if (!validationResult.isSuccess) {
      return validationResult;
    }

    // Update player position
    player.x = newX;
    player.y = newY;

    // Process tile interactions
    final element = gameState.maze.getElementAt(newX, newY);
    return _processElementInteraction(gameState, player, element);
  }

  static InteractionResponse _processElementInteraction(
    GameState gameState,
    Player player,
    PuzzleElement element,
  ) {
    switch (element.type) {
      case PuzzleElementType.key:
        return _processKeyInteraction(gameState, player, element);
      case PuzzleElementType.trap:
        return _processTrapInteraction(gameState, player, element);
      case PuzzleElementType.pressurePlate:
        return _processPressurePlateInteraction(gameState, player, element);
      case PuzzleElementType.mirrorTile:
        return _processMirrorTileInteraction(gameState, player, element);
      case PuzzleElementType.exit:
        return _processExitInteraction(gameState, player, element);
      default:
        return const InteractionResponse(
          result: InteractionResult.success,
          message: 'Moved successfully',
        );
    }
  }

  static InteractionResponse _processKeyInteraction(
    GameState gameState,
    Player player,
    PuzzleElement keyElement,
  ) {
    if (keyElement.properties['collected'] == true) {
      return const InteractionResponse(
        result: InteractionResult.success,
        message: 'Key already collected',
      );
    }

    final keyId = keyElement.properties['keyId'] as String;
    player.addToInventory(keyId);
    keyElement.collect();
    
    gameState.addToLog('${player.name} collected key: $keyId');

    return InteractionResponse(
      result: InteractionResult.keyCollected,
      message: 'Collected key: $keyId',
      data: {'keyId': keyId},
    );
  }

  static InteractionResponse _processTrapInteraction(
    GameState gameState,
    Player player,
    PuzzleElement trapElement,
  ) {
    if (trapElement.properties['triggered'] == true) {
      return const InteractionResponse(
        result: InteractionResult.success,
        message: 'Trap already triggered',
      );
    }

    trapElement.trigger();
    player.loseLife();
    gameState.totalLivesLost++;
    
    gameState.addToLog('${player.name} triggered a trap! Lives remaining: ${player.lives}');

    return InteractionResponse(
      result: InteractionResult.trapTriggered,
      message: 'Trap triggered! Lost a life.',
      data: {'livesRemaining': player.lives},
    );
  }

  static InteractionResponse _processPressurePlateInteraction(
    GameState gameState,
    Player player,
    PuzzleElement plateElement,
  ) {
    if (plateElement.properties['activated'] == true) {
      return const InteractionResponse(
        result: InteractionResult.success,
        message: 'Pressure plate already activated',
      );
    }

    plateElement.activate();
    final triggerId = plateElement.properties['triggerId'] as String;
    
    // Find and unlock related doors or activate mechanisms
    _activateTriggeredElements(gameState, triggerId);
    
    gameState.addToLog('${player.name} activated pressure plate: $triggerId');

    return InteractionResponse(
      result: InteractionResult.pressurePlateActivated,
      message: 'Pressure plate activated',
      data: {'triggerId': triggerId},
    );
  }

  static InteractionResponse _processMirrorTileInteraction(
    GameState gameState,
    Player player,
    PuzzleElement mirrorElement,
  ) {
    if (mirrorElement.properties['triggered'] == true) {
      return const InteractionResponse(
        result: InteractionResult.success,
        message: 'Mirror tile already activated',
      );
    }

    mirrorElement.trigger();
    final duration = mirrorElement.properties['duration'] as int? ?? 5;
    
    // Store mirror effect in game data
    gameState.gameData['mirrorEffect'] = {
      'active': true,
      'endTime': DateTime.now().add(Duration(seconds: duration)).toIso8601String(),
      'playerId': player.id,
    };
    
    gameState.addToLog('${player.name} activated mirror tile! Controls reversed for ${duration}s');

    return InteractionResponse(
      result: InteractionResult.mirrorTileActivated,
      message: 'Mirror tile activated! Controls reversed.',
      data: {'duration': duration},
    );
  }

  static InteractionResponse _processExitInteraction(
    GameState gameState,
    Player player,
    PuzzleElement exitElement,
  ) {
    // Check if player has all required keys
    final missingKeys = gameState.maze.requiredKeys
        .where((keyId) => !player.hasKey(keyId))
        .toList();

    if (missingKeys.isNotEmpty) {
      return InteractionResponse(
        result: InteractionResult.requiresKey,
        message: 'Need keys: ${missingKeys.join(', ')}',
        data: {'missingKeys': missingKeys},
      );
    }

    gameState.addToLog('${player.name} reached the exit with all keys!');

    return const InteractionResponse(
      result: InteractionResult.exitReached,
      message: 'Victory! All objectives completed.',
    );
  }

  static void _activateTriggeredElements(GameState gameState, String triggerId) {
    // Find elements that should be activated by this trigger
    for (int y = 0; y < gameState.maze.height; y++) {
      for (int x = 0; x < gameState.maze.width; x++) {
        final element = gameState.maze.getElementAt(x, y);
        
        // Check if this element should be triggered
        if (element.type == PuzzleElementType.door) {
          final doorTriggerId = element.properties['triggerId'] as String?;
          if (doorTriggerId == triggerId) {
            element.unlock();
            gameState.addToLog('Door unlocked by pressure plate');
          }
        }
      }
    }
  }

  static InteractionResponse processDirectInteraction(
    GameState gameState,
    String playerId,
    int x,
    int y,
  ) {
    final player = gameState.getPlayerById(playerId);
    if (player == null) {
      return const InteractionResponse(
        result: InteractionResult.invalid,
        message: 'Player not found',
      );
    }

    // Check if position is adjacent to player
    final distance = (player.x - x).abs() + (player.y - y).abs();
    if (distance > 1) {
      return const InteractionResponse(
        result: InteractionResult.invalid,
        message: 'Too far to interact',
      );
    }

    final element = gameState.maze.getElementAt(x, y);
    
    switch (element.type) {
      case PuzzleElementType.door:
        return _processDoorInteraction(gameState, player, element);
      case PuzzleElementType.pressurePlate:
        // Can manually activate pressure plates
        return _processPressurePlateInteraction(gameState, player, element);
      default:
        return const InteractionResponse(
          result: InteractionResult.invalid,
          message: 'Cannot interact with this element',
        );
    }
  }

  static InteractionResponse _processDoorInteraction(
    GameState gameState,
    Player player,
    PuzzleElement doorElement,
  ) {
    if (doorElement.state == ElementState.unlocked) {
      return const InteractionResponse(
        result: InteractionResult.success,
        message: 'Door is already unlocked',
      );
    }

    final requiredKeyId = doorElement.properties['requiredKeyId'] as String;
    
    if (!player.hasKey(requiredKeyId)) {
      return InteractionResponse(
        result: InteractionResult.requiresKey,
        message: 'Need key: $requiredKeyId',
        data: {'requiredKey': requiredKeyId},
      );
    }

    // Unlock door and consume key
    doorElement.unlock();
    player.removeFromInventory(requiredKeyId);
    
    gameState.addToLog('${player.name} unlocked door with key: $requiredKeyId');

    return InteractionResponse(
      result: InteractionResult.doorUnlocked,
      message: 'Door unlocked!',
      data: {'keyUsed': requiredKeyId},
    );
  }

  // Utility methods
  static bool isMirrorEffectActive(GameState gameState, String playerId) {
    final mirrorEffect = gameState.gameData['mirrorEffect'] as Map<String, dynamic>?;
    
    if (mirrorEffect == null || mirrorEffect['active'] != true) {
      return false;
    }

    if (mirrorEffect['playerId'] != playerId) {
      return false;
    }

    final endTimeStr = mirrorEffect['endTime'] as String;
    final endTime = DateTime.parse(endTimeStr);
    
    if (DateTime.now().isAfter(endTime)) {
      // Effect expired, clean up
      gameState.gameData.remove('mirrorEffect');
      return false;
    }

    return true;
  }

  static List<Position> getValidMoves(Maze maze, Player player) {
    final validMoves = <Position>[];
    final directions = [
      Position(player.x + 1, player.y),     // Right
      Position(player.x - 1, player.y),     // Left
      Position(player.x, player.y + 1),     // Down
      Position(player.x, player.y - 1),     // Up
    ];

    for (final pos in directions) {
      final result = validateMovement(maze, player, pos.x, pos.y);
      if (result.isSuccess) {
        validMoves.add(pos);
      }
    }

    return validMoves;
  }

  static List<Position> getInteractablePositions(Maze maze, Player player) {
    final interactable = <Position>[];
    final directions = [
      Position(player.x + 1, player.y),     // Right
      Position(player.x - 1, player.y),     // Left
      Position(player.x, player.y + 1),     // Down
      Position(player.x, player.y - 1),     // Up
      Position(player.x, player.y),         // Current position
    ];

    for (final pos in directions) {
      if (maze.isValidPosition(pos.x, pos.y)) {
        final element = maze.getElementAt(pos.x, pos.y);
        if (element.isInteractable) {
          interactable.add(pos);
        }
      }
    }

    return interactable;
  }

  static bool canCompleteGame(GameState gameState) {
    final walker = gameState.getWalker();
    if (walker == null) return false;

    // Check if walker has all required keys
    final hasAllKeys = gameState.maze.requiredKeys
        .every((keyId) => walker.hasKey(keyId));

    if (!hasAllKeys) return false;

    // Check if there's a path to the exit
    // This would require pathfinding implementation
    return true; // Simplified for now
  }
}
