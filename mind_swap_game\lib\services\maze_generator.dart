import 'dart:math';
import '../models/models.dart';

class MazeGenerator {
  static const List<Position> _directions = [
    Position(0, -2), // Up
    Position(2, 0),  // Right
    Position(0, 2),  // Down
    Position(-2, 0), // Left
  ];

  static Maze generateMaze({
    MazeDifficulty difficulty = MazeDifficulty.medium,
    int? seed,
  }) {
    final random = Random(seed);
    final dimensions = _getDimensions(difficulty);
    final width = dimensions['width']!;
    final height = dimensions['height']!;

    // Create maze with recursive backtracking
    final maze = _createBaseMaze(width, height, difficulty);
    final processedMaze = _generatePaths(maze, random);
    final finalMaze = _addPuzzleElements(processedMaze, random);
    
    return _validateAndOptimize(finalMaze, random);
  }

  static Map<String, int> _getDimensions(MazeDifficulty difficulty) {
    switch (difficulty) {
      case MazeDifficulty.easy:
        return {'width': 7, 'height': 7}; // Odd numbers for better maze generation
      case MazeDifficulty.medium:
        return {'width': 9, 'height': 9};
      case MazeDifficulty.hard:
        return {'width': 11, 'height': 11};
    }
  }

  static Maze _createBaseMaze(int width, int height, MazeDifficulty difficulty) {
    // Initialize with all walls
    final grid = List.generate(
      height,
      (y) => List.generate(
        width,
        (x) => PuzzleElement.wall(x: x, y: y),
      ),
    );

    return Maze(
      width: width,
      height: height,
      difficulty: difficulty,
      grid: grid,
      startPosition: const Position(1, 1),
      exitPosition: Position(width - 2, height - 2),
    );
  }

  static Maze _generatePaths(Maze maze, Random random) {
    final grid = maze.grid;
    final width = maze.width;
    final height = maze.height;
    final visited = List.generate(height, (_) => List.filled(width, false));
    final stack = <Position>[];

    // Start from position (1, 1)
    final start = Position(1, 1);
    grid[start.y][start.x] = PuzzleElement.empty(x: start.x, y: start.y);
    visited[start.y][start.x] = true;
    stack.add(start);

    while (stack.isNotEmpty) {
      final current = stack.last;
      final neighbors = _getUnvisitedNeighbors(current, visited, width, height);

      if (neighbors.isNotEmpty) {
        // Choose random neighbor
        final next = neighbors[random.nextInt(neighbors.length)];
        
        // Remove wall between current and next
        final wallX = current.x + (next.x - current.x) ~/ 2;
        final wallY = current.y + (next.y - current.y) ~/ 2;
        
        grid[wallY][wallX] = PuzzleElement.empty(x: wallX, y: wallY);
        grid[next.y][next.x] = PuzzleElement.empty(x: next.x, y: next.y);
        
        visited[next.y][next.x] = true;
        stack.add(next);
      } else {
        stack.removeLast();
      }
    }

    // Ensure exit is accessible
    grid[maze.exitPosition.y][maze.exitPosition.x] = 
        PuzzleElement.exit(x: maze.exitPosition.x, y: maze.exitPosition.y);

    return maze.copyWith(grid: grid);
  }

  static List<Position> _getUnvisitedNeighbors(
    Position current, 
    List<List<bool>> visited, 
    int width, 
    int height
  ) {
    final neighbors = <Position>[];
    
    for (final direction in _directions) {
      final newX = current.x + direction.x;
      final newY = current.y + direction.y;
      
      if (newX > 0 && newX < width - 1 && 
          newY > 0 && newY < height - 1 && 
          !visited[newY][newX]) {
        neighbors.add(Position(newX, newY));
      }
    }
    
    return neighbors;
  }

  static Maze _addPuzzleElements(Maze maze, Random random) {
    final grid = maze.grid;
    final elements = <String, PuzzleElement>{};
    final requiredKeys = <String>[];
    
    final elementCounts = _getElementCounts(maze.difficulty);
    final emptyPositions = _getEmptyPositions(grid);
    
    // Shuffle positions for random placement
    emptyPositions.shuffle(random);
    
    int positionIndex = 0;
    
    // Add keys and corresponding doors
    for (int i = 0; i < elementCounts['keys']!; i++) {
      if (positionIndex + 1 < emptyPositions.length) {
        final keyId = 'key_${i + 1}';
        final keyColor = _getKeyColor(i);
        
        // Place key
        final keyPos = emptyPositions[positionIndex++];
        final key = PuzzleElement.key(
          x: keyPos.x, 
          y: keyPos.y, 
          keyId: keyId,
          color: keyColor,
        );
        grid[keyPos.y][keyPos.x] = key;
        elements[key.id] = key;
        requiredKeys.add(keyId);
        
        // Place corresponding door (not too close to key)
        final doorPos = _findSuitableDoorPosition(
          emptyPositions, 
          positionIndex, 
          keyPos, 
          grid
        );
        if (doorPos != null) {
          final door = PuzzleElement.door(
            x: doorPos.x, 
            y: doorPos.y, 
            requiredKeyId: keyId,
            color: keyColor,
          );
          grid[doorPos.y][doorPos.x] = door;
          elements[door.id] = door;
          
          // Remove door position from available positions
          emptyPositions.removeWhere((pos) => pos.x == doorPos.x && pos.y == doorPos.y);
        }
      }
    }
    
    // Add traps
    for (int i = 0; i < elementCounts['traps']! && positionIndex < emptyPositions.length; i++) {
      final pos = emptyPositions[positionIndex++];
      final trap = PuzzleElement.trap(x: pos.x, y: pos.y);
      grid[pos.y][pos.x] = trap;
      elements[trap.id] = trap;
    }
    
    // Add pressure plates
    for (int i = 0; i < elementCounts['plates']! && positionIndex < emptyPositions.length; i++) {
      final pos = emptyPositions[positionIndex++];
      final plate = PuzzleElement.pressurePlate(
        x: pos.x, 
        y: pos.y, 
        triggerId: 'trigger_${i + 1}'
      );
      grid[pos.y][pos.x] = plate;
      elements[plate.id] = plate;
    }
    
    // Add mirror tiles for higher difficulties
    if (maze.difficulty != MazeDifficulty.easy) {
      final mirrorCount = maze.difficulty == MazeDifficulty.hard ? 2 : 1;
      for (int i = 0; i < mirrorCount && positionIndex < emptyPositions.length; i++) {
        final pos = emptyPositions[positionIndex++];
        final mirror = PuzzleElement.mirrorTile(x: pos.x, y: pos.y);
        grid[pos.y][pos.x] = mirror;
        elements[mirror.id] = mirror;
      }
    }
    
    // Add fog zones for hard difficulty
    if (maze.difficulty == MazeDifficulty.hard) {
      for (int i = 0; i < 1 && positionIndex < emptyPositions.length; i++) {
        final pos = emptyPositions[positionIndex++];
        final fog = PuzzleElement.fogZone(x: pos.x, y: pos.y, radius: 2);
        grid[pos.y][pos.x] = fog;
        elements[fog.id] = fog;
      }
    }
    
    return maze.copyWith(
      grid: grid,
      elements: elements,
      requiredKeys: requiredKeys,
    );
  }

  static Map<String, int> _getElementCounts(MazeDifficulty difficulty) {
    switch (difficulty) {
      case MazeDifficulty.easy:
        return {'keys': 1, 'traps': 2, 'plates': 1};
      case MazeDifficulty.medium:
        return {'keys': 2, 'traps': 3, 'plates': 2};
      case MazeDifficulty.hard:
        return {'keys': 3, 'traps': 4, 'plates': 3};
    }
  }

  static String _getKeyColor(int index) {
    const colors = ['gold', 'silver', 'bronze', 'red', 'blue', 'green'];
    return colors[index % colors.length];
  }

  static List<Position> _getEmptyPositions(List<List<PuzzleElement>> grid) {
    final positions = <Position>[];
    
    for (int y = 0; y < grid.length; y++) {
      for (int x = 0; x < grid[y].length; x++) {
        if (grid[y][x].type == PuzzleElementType.empty) {
          // Avoid start and exit positions
          if (!((x == 1 && y == 1) || 
                (x == grid[y].length - 2 && y == grid.length - 2))) {
            positions.add(Position(x, y));
          }
        }
      }
    }
    
    return positions;
  }

  static Position? _findSuitableDoorPosition(
    List<Position> positions, 
    int startIndex, 
    Position keyPos, 
    List<List<PuzzleElement>> grid
  ) {
    // Find a position that's not too close to the key
    for (int i = startIndex; i < positions.length; i++) {
      final pos = positions[i];
      final distance = (pos.x - keyPos.x).abs() + (pos.y - keyPos.y).abs();
      
      if (distance >= 3) { // Manhattan distance of at least 3
        return pos;
      }
    }
    
    // If no suitable position found, return the first available
    return startIndex < positions.length ? positions[startIndex] : null;
  }

  static Maze _validateAndOptimize(Maze maze, Random random) {
    // Ensure there's a path from start to exit
    if (!_hasPathToExit(maze)) {
      // Create additional paths if needed
      return _createAdditionalPaths(maze, random);
    }
    
    return maze;
  }

  static bool _hasPathToExit(Maze maze) {
    final visited = List.generate(
      maze.height, 
      (_) => List.filled(maze.width, false)
    );
    
    return _dfs(
      maze, 
      maze.startPosition.x, 
      maze.startPosition.y, 
      visited
    );
  }

  static bool _dfs(Maze maze, int x, int y, List<List<bool>> visited) {
    if (x == maze.exitPosition.x && y == maze.exitPosition.y) {
      return true;
    }
    
    if (x < 0 || x >= maze.width || y < 0 || y >= maze.height || 
        visited[y][x] || !maze.isWalkable(x, y)) {
      return false;
    }
    
    visited[y][x] = true;
    
    // Check all four directions
    return _dfs(maze, x + 1, y, visited) ||
           _dfs(maze, x - 1, y, visited) ||
           _dfs(maze, x, y + 1, visited) ||
           _dfs(maze, x, y - 1, visited);
  }

  static Maze _createAdditionalPaths(Maze maze, Random random) {
    // Simple implementation: create a direct path if none exists
    final grid = maze.grid;
    
    // Create a simple path from start towards exit
    int currentX = maze.startPosition.x;
    int currentY = maze.startPosition.y;
    
    while (currentX != maze.exitPosition.x || currentY != maze.exitPosition.y) {
      if (currentX < maze.exitPosition.x) {
        currentX++;
      } else if (currentX > maze.exitPosition.x) {
        currentX--;
      } else if (currentY < maze.exitPosition.y) {
        currentY++;
      } else if (currentY > maze.exitPosition.y) {
        currentY--;
      }
      
      if (grid[currentY][currentX].type == PuzzleElementType.wall) {
        grid[currentY][currentX] = PuzzleElement.empty(x: currentX, y: currentY);
      }
    }
    
    return maze.copyWith(grid: grid);
  }

  // Utility method to generate a simple test maze
  static Maze generateTestMaze() {
    return generateMaze(
      difficulty: MazeDifficulty.easy,
      seed: 12345, // Fixed seed for consistent testing
    );
  }
}
