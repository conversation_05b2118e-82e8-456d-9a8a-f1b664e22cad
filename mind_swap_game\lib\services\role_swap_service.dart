import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import '../models/models.dart';

enum SwapPhase {
  normal,      // Normal gameplay
  warning,     // 10 seconds before swap
  countdown,   // 3-2-1 countdown
  swapping,    // Role swap in progress
  complete,    // Swap completed
}

class RoleSwapEvent {
  final SwapPhase phase;
  final int timeRemaining;
  final int swapNumber;
  final DateTime timestamp;
  final String? message;

  const RoleSwapEvent({
    required this.phase,
    required this.timeRemaining,
    required this.swapNumber,
    required this.timestamp,
    this.message,
  });
}

class RoleSwapService {
  static const int defaultSwapInterval = 60; // seconds
  static const int warningThreshold = 10;    // seconds
  static const int countdownThreshold = 3;   // seconds

  Timer? _timer;
  int _timeUntilSwap = defaultSwapInterval;
  int _swapCount = 0;
  SwapPhase _currentPhase = SwapPhase.normal;
  bool _isRunning = false;

  final StreamController<RoleSwapEvent> _eventController = 
      StreamController<RoleSwapEvent>.broadcast();

  // Getters
  Stream<RoleSwapEvent> get eventStream => _eventController.stream;
  int get timeUntilSwap => _timeUntilSwap;
  int get swapCount => _swapCount;
  SwapPhase get currentPhase => _currentPhase;
  bool get isRunning => _isRunning;
  bool get isWarningPhase => _currentPhase == SwapPhase.warning;
  bool get isCountdownPhase => _currentPhase == SwapPhase.countdown;
  bool get isSwapping => _currentPhase == SwapPhase.swapping;

  // Timer control
  void start({int? customInterval}) {
    if (_isRunning) return;

    _timeUntilSwap = customInterval ?? defaultSwapInterval;
    _isRunning = true;
    _currentPhase = SwapPhase.normal;

    _timer = Timer.periodic(const Duration(seconds: 1), _onTick);
    
    _emitEvent(SwapPhase.normal, 'Role swap timer started');
  }

  void pause() {
    if (!_isRunning) return;

    _isRunning = false;
    _timer?.cancel();
    _timer = null;

    _emitEvent(_currentPhase, 'Role swap timer paused');
  }

  void resume() {
    if (_isRunning) return;

    _isRunning = true;
    _timer = Timer.periodic(const Duration(seconds: 1), _onTick);

    _emitEvent(_currentPhase, 'Role swap timer resumed');
  }

  void stop() {
    _isRunning = false;
    _timer?.cancel();
    _timer = null;
    _currentPhase = SwapPhase.normal;
    _timeUntilSwap = defaultSwapInterval;

    _emitEvent(SwapPhase.normal, 'Role swap timer stopped');
  }

  void reset({int? customInterval}) {
    stop();
    _swapCount = 0;
    _timeUntilSwap = customInterval ?? defaultSwapInterval;
    _currentPhase = SwapPhase.normal;

    _emitEvent(SwapPhase.normal, 'Role swap timer reset');
  }

  void forceSwap() {
    if (!_isRunning) return;

    _timeUntilSwap = 0;
    _onTick(_timer!);
  }

  // Timer tick handler
  void _onTick(Timer timer) {
    if (!_isRunning) return;

    _timeUntilSwap--;

    // Determine current phase
    final previousPhase = _currentPhase;
    _updatePhase();

    // Emit events for phase changes
    if (previousPhase != _currentPhase) {
      _onPhaseChange(previousPhase, _currentPhase);
    }

    // Handle swap trigger
    if (_timeUntilSwap <= 0) {
      _triggerSwap();
    } else {
      _emitEvent(_currentPhase, null);
    }
  }

  void _updatePhase() {
    if (_timeUntilSwap <= 0) {
      _currentPhase = SwapPhase.swapping;
    } else if (_timeUntilSwap <= countdownThreshold) {
      _currentPhase = SwapPhase.countdown;
    } else if (_timeUntilSwap <= warningThreshold) {
      _currentPhase = SwapPhase.warning;
    } else {
      _currentPhase = SwapPhase.normal;
    }
  }

  void _onPhaseChange(SwapPhase from, SwapPhase to) {
    String? message;

    switch (to) {
      case SwapPhase.warning:
        message = 'Role swap in ${_timeUntilSwap} seconds!';
        break;
      case SwapPhase.countdown:
        message = 'Role swap in ${_timeUntilSwap}...';
        break;
      case SwapPhase.swapping:
        message = 'Swapping roles now!';
        break;
      case SwapPhase.complete:
        message = 'Role swap complete!';
        break;
      default:
        break;
    }

    _emitEvent(to, message);
  }

  void _triggerSwap() {
    _swapCount++;
    _currentPhase = SwapPhase.swapping;
    
    _emitEvent(SwapPhase.swapping, 'Roles are swapping!');

    // Simulate swap duration
    Timer(const Duration(milliseconds: 1500), () {
      _completeSwap();
    });
  }

  void _completeSwap() {
    _currentPhase = SwapPhase.complete;
    _emitEvent(SwapPhase.complete, 'Swap #$_swapCount complete!');

    // Reset timer for next swap
    Timer(const Duration(milliseconds: 1000), () {
      _timeUntilSwap = defaultSwapInterval;
      _currentPhase = SwapPhase.normal;
      _emitEvent(SwapPhase.normal, 'Next swap cycle started');
    });
  }

  void _emitEvent(SwapPhase phase, String? message) {
    final event = RoleSwapEvent(
      phase: phase,
      timeRemaining: _timeUntilSwap,
      swapNumber: _swapCount,
      timestamp: DateTime.now(),
      message: message,
    );

    _eventController.add(event);

    if (kDebugMode && message != null) {
      print('RoleSwapService: $message');
    }
  }

  // Utility methods
  String getFormattedTime() {
    final minutes = _timeUntilSwap ~/ 60;
    final seconds = _timeUntilSwap % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  String getCountdownText() {
    if (_currentPhase == SwapPhase.countdown) {
      return _timeUntilSwap.toString();
    } else if (_currentPhase == SwapPhase.swapping) {
      return 'SWAP!';
    }
    return '';
  }

  double getProgressPercentage() {
    return 1.0 - (_timeUntilSwap / defaultSwapInterval);
  }

  bool shouldShowWarning() {
    return _currentPhase == SwapPhase.warning || _currentPhase == SwapPhase.countdown;
  }

  bool shouldShowCountdown() {
    return _currentPhase == SwapPhase.countdown || _currentPhase == SwapPhase.swapping;
  }

  // Animation helpers
  double getWarningOpacity() {
    if (_currentPhase == SwapPhase.warning) {
      // Pulse effect during warning
      final pulseSpeed = 2.0;
      final time = DateTime.now().millisecondsSinceEpoch / 1000.0;
      return 0.5 + 0.5 * (1.0 + math.sin(time * pulseSpeed)) / 2.0;
    } else if (_currentPhase == SwapPhase.countdown) {
      return 1.0;
    }
    return 0.0;
  }

  double getCountdownScale() {
    if (_currentPhase == SwapPhase.countdown) {
      // Scale effect during countdown
      final progress = (_timeUntilSwap % 1000) / 1000.0;
      return 1.0 + 0.5 * (1.0 - progress);
    }
    return 1.0;
  }

  // Integration with GameState
  void syncWithGameTimer(GameTimer gameTimer) {
    if (gameTimer.isRunning && !_isRunning) {
      start();
    } else if (!gameTimer.isRunning && _isRunning) {
      pause();
    }

    // Sync time if there's a discrepancy
    if ((_timeUntilSwap - gameTimer.timeUntilSwap).abs() > 1) {
      _timeUntilSwap = gameTimer.timeUntilSwap;
    }
  }

  void updateFromGameState(GameState gameState) {
    _swapCount = gameState.totalRoleSwaps;
    
    if (gameState.timer.shouldSwapRoles && _currentPhase != SwapPhase.swapping) {
      _triggerSwap();
    }
  }

  // Cleanup
  void dispose() {
    stop();
    _eventController.close();
  }
}


