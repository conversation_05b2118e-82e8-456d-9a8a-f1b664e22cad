import '../models/models.dart';
import 'collision_service.dart';

enum GameCondition {
  victory,
  defeat,
  timeUp,
  outOfLives,
  playerDisconnected,
  objectiveComplete,
  trapTriggered,
  keyCollected,
  doorUnlocked,
  exitReached,
}

class GameConditionResult {
  final GameCondition condition;
  final bool isGameEnding;
  final String message;
  final Map<String, dynamic> data;

  const GameConditionResult({
    required this.condition,
    required this.isGameEnding,
    required this.message,
    this.data = const {},
  });

  bool get isVictory => condition == GameCondition.victory;
  bool get isDefeat => condition == GameCondition.defeat;
}

class GameLogicService {
  // Check all game conditions and return the most critical one
  static GameConditionResult checkGameConditions(GameState gameState) {
    // Check for immediate game-ending conditions first
    final timeResult = checkTimeConditions(gameState);
    if (timeResult.isGameEnding) return timeResult;

    final lifeResult = checkLifeConditions(gameState);
    if (lifeResult.isGameEnding) return lifeResult;

    final victoryResult = checkVictoryConditions(gameState);
    if (victoryResult.isGameEnding) return victoryResult;

    final connectionResult = checkConnectionConditions(gameState);
    if (connectionResult.isGameEnding) return connectionResult;

    // Return the most relevant non-ending condition
    return victoryResult;
  }

  // Check victory conditions
  static GameConditionResult checkVictoryConditions(GameState gameState) {
    final walker = gameState.getWalker();
    if (walker == null) {
      return const GameConditionResult(
        condition: GameCondition.defeat,
        isGameEnding: true,
        message: 'No walker found',
      );
    }

    // Check if walker is at exit
    final currentElement = gameState.maze.getElementAt(walker.x, walker.y);
    if (currentElement.type == PuzzleElementType.exit) {
      // Check if all required keys are collected
      final missingKeys = gameState.maze.requiredKeys
          .where((keyId) => !walker.hasKey(keyId))
          .toList();

      if (missingKeys.isEmpty) {
        return const GameConditionResult(
          condition: GameCondition.victory,
          isGameEnding: true,
          message: 'Victory! All objectives completed and exit reached!',
        );
      } else {
        return GameConditionResult(
          condition: GameCondition.exitReached,
          isGameEnding: false,
          message: 'At exit but missing keys: ${missingKeys.join(', ')}',
          data: {'missingKeys': missingKeys},
        );
      }
    }

    // Check progress towards victory
    final collectedKeys = walker.inventory.length;
    final totalKeys = gameState.maze.requiredKeys.length;
    final progress = totalKeys > 0 ? collectedKeys / totalKeys : 0.0;

    return GameConditionResult(
      condition: GameCondition.objectiveComplete,
      isGameEnding: false,
      message: 'Progress: $collectedKeys/$totalKeys keys collected',
      data: {
        'progress': progress,
        'collectedKeys': collectedKeys,
        'totalKeys': totalKeys,
      },
    );
  }

  // Check life-related conditions
  static GameConditionResult checkLifeConditions(GameState gameState) {
    final totalLives = gameState.totalLives;
    
    if (totalLives <= 0) {
      return const GameConditionResult(
        condition: GameCondition.outOfLives,
        isGameEnding: true,
        message: 'Game Over! No lives remaining.',
      );
    }

    if (totalLives == 1) {
      return const GameConditionResult(
        condition: GameCondition.outOfLives,
        isGameEnding: false,
        message: 'Warning! Only 1 life remaining.',
      );
    }

    return GameConditionResult(
      condition: GameCondition.objectiveComplete,
      isGameEnding: false,
      message: 'Lives remaining: $totalLives',
      data: {'lives': totalLives},
    );
  }

  // Check time-related conditions
  static GameConditionResult checkTimeConditions(GameState gameState) {
    final remainingTime = gameState.timer.remainingTime;
    
    if (remainingTime <= 0) {
      return const GameConditionResult(
        condition: GameCondition.timeUp,
        isGameEnding: true,
        message: 'Time\'s up! Game Over.',
      );
    }

    if (remainingTime <= 30) {
      return GameConditionResult(
        condition: GameCondition.timeUp,
        isGameEnding: false,
        message: 'Warning! Only ${remainingTime}s remaining.',
        data: {'remainingTime': remainingTime},
      );
    }

    return GameConditionResult(
      condition: GameCondition.objectiveComplete,
      isGameEnding: false,
      message: 'Time remaining: ${_formatTime(remainingTime)}',
      data: {'remainingTime': remainingTime},
    );
  }

  // Check connection-related conditions
  static GameConditionResult checkConnectionConditions(GameState gameState) {
    final connectedPlayers = gameState.players
        .where((p) => p.status == PlayerStatus.connected || p.status == PlayerStatus.ready)
        .length;

    if (connectedPlayers < 2) {
      return const GameConditionResult(
        condition: GameCondition.playerDisconnected,
        isGameEnding: true,
        message: 'Game ended due to player disconnection.',
      );
    }

    return GameConditionResult(
      condition: GameCondition.objectiveComplete,
      isGameEnding: false,
      message: 'Both players connected',
      data: {'connectedPlayers': connectedPlayers},
    );
  }

  // Validate if the game can be completed
  static bool canGameBeCompleted(GameState gameState) {
    final walker = gameState.getWalker();
    if (walker == null) return false;

    // Check if all required keys are still available
    final availableKeys = <String>[];
    for (int y = 0; y < gameState.maze.height; y++) {
      for (int x = 0; x < gameState.maze.width; x++) {
        final element = gameState.maze.getElementAt(x, y);
        if (element.type == PuzzleElementType.key && 
            element.properties['collected'] != true) {
          final keyId = element.properties['keyId'] as String;
          availableKeys.add(keyId);
        }
      }
    }

    // Add keys already in inventory
    availableKeys.addAll(walker.inventory);

    // Check if all required keys can be obtained
    final canGetAllKeys = gameState.maze.requiredKeys
        .every((keyId) => availableKeys.contains(keyId));

    if (!canGetAllKeys) return false;

    // Check if there's a path to the exit
    final exitPosition = gameState.maze.exitPosition;
    final walkerPosition = Position(walker.x, walker.y);
    
    final pathToExit = CollisionService.findPath(
      gameState.maze,
      walkerPosition,
      exitPosition,
      player: walker,
      avoidTraps: false, // Player might need to risk traps
    );

    return pathToExit != null;
  }

  // Calculate game difficulty score
  static double calculateDifficultyScore(GameState gameState) {
    double score = 0.0;

    // Base score from maze size
    final mazeSize = gameState.maze.width * gameState.maze.height;
    score += mazeSize * 0.1;

    // Score from number of required keys
    score += gameState.maze.requiredKeys.length * 10.0;

    // Score from number of traps
    int trapCount = 0;
    for (int y = 0; y < gameState.maze.height; y++) {
      for (int x = 0; x < gameState.maze.width; x++) {
        final element = gameState.maze.getElementAt(x, y);
        if (element.type == PuzzleElementType.trap) {
          trapCount++;
        }
      }
    }
    score += trapCount * 5.0;

    // Score from time pressure
    final timeRatio = gameState.timer.remainingTime / gameState.timer.totalTime;
    score += (1.0 - timeRatio) * 20.0;

    // Score from lives lost
    score += gameState.totalLivesLost * 15.0;

    return score;
  }

  // Calculate completion percentage
  static double calculateCompletionPercentage(GameState gameState) {
    final walker = gameState.getWalker();
    if (walker == null) return 0.0;

    double completion = 0.0;

    // Keys collected (60% of completion)
    final collectedKeys = walker.inventory.length;
    final totalKeys = gameState.maze.requiredKeys.length;
    if (totalKeys > 0) {
      completion += (collectedKeys / totalKeys) * 0.6;
    }

    // Distance to exit (40% of completion)
    final walkerPos = Position(walker.x, walker.y);
    final exitPos = gameState.maze.exitPosition;
    final maxDistance = gameState.maze.width + gameState.maze.height;
    final currentDistance = CollisionService.manhattanDistance(walkerPos, exitPos);
    final distanceProgress = 1.0 - (currentDistance / maxDistance);
    completion += distanceProgress * 0.4;

    return completion.clamp(0.0, 1.0);
  }

  // Get hints for the current game state
  static List<String> getGameHints(GameState gameState) {
    final hints = <String>[];
    final walker = gameState.getWalker();
    
    if (walker == null) return hints;

    // Key collection hints
    final missingKeys = gameState.maze.requiredKeys
        .where((keyId) => !walker.hasKey(keyId))
        .toList();
    
    if (missingKeys.isNotEmpty) {
      hints.add('Collect missing keys: ${missingKeys.join(', ')}');
    }

    // Time pressure hints
    if (gameState.timer.remainingTime < 60) {
      hints.add('Time is running out! Move quickly.');
    }

    // Life hints
    if (gameState.totalLives <= 2) {
      hints.add('Be careful! Few lives remaining.');
    }

    // Navigation hints
    final exitPos = gameState.maze.exitPosition;
    final walkerPos = Position(walker.x, walker.y);
    final distance = CollisionService.manhattanDistance(walkerPos, exitPos);
    
    if (distance > 10) {
      hints.add('Exit is far away. Plan your route carefully.');
    } else if (distance <= 3 && missingKeys.isEmpty) {
      hints.add('Exit is nearby! You can win!');
    }

    return hints;
  }

  // Check if a specific action is safe
  static bool isActionSafe(GameState gameState, int x, int y) {
    if (!gameState.maze.isValidPosition(x, y)) return false;

    final element = gameState.maze.getElementAt(x, y);
    
    // Check for traps
    if (element.type == PuzzleElementType.trap && 
        element.properties['triggered'] != true) {
      return false;
    }

    // Check for walls
    if (element.type == PuzzleElementType.wall) {
      return false;
    }

    // Check for locked doors
    if (element.type == PuzzleElementType.door && 
        element.state != ElementState.unlocked) {
      final walker = gameState.getWalker();
      if (walker != null) {
        final requiredKeyId = element.properties['requiredKeyId'] as String;
        return walker.hasKey(requiredKeyId);
      }
      return false;
    }

    return true;
  }

  // Get recommended next actions
  static List<String> getRecommendedActions(GameState gameState) {
    final actions = <String>[];
    final walker = gameState.getWalker();
    
    if (walker == null) return actions;

    // Find nearest uncollected key
    final missingKeys = gameState.maze.requiredKeys
        .where((keyId) => !walker.hasKey(keyId))
        .toList();

    if (missingKeys.isNotEmpty) {
      actions.add('Find and collect keys');
    }

    // Check if at exit
    final currentElement = gameState.maze.getElementAt(walker.x, walker.y);
    if (currentElement.type == PuzzleElementType.exit) {
      if (missingKeys.isEmpty) {
        actions.add('Victory! You\'ve completed all objectives!');
      } else {
        actions.add('Collect remaining keys before exiting');
      }
    } else {
      if (missingKeys.isEmpty) {
        actions.add('Head to the exit');
      }
    }

    return actions;
  }

  static String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }
}
