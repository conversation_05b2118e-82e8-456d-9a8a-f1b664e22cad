import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/models.dart';

enum MultiplayerEvent {
  playerJoined,
  playerLeft,
  gameStateUpdated,
  playerMoved,
  playerInteracted,
  roleSwapped,
  gameStarted,
  gameEnded,
  chatMessage,
}

class MultiplayerMessage {
  final MultiplayerEvent event;
  final String senderId;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  MultiplayerMessage({
    required this.event,
    required this.senderId,
    required this.data,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'event': event.name,
      'senderId': senderId,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory MultiplayerMessage.fromJson(Map<String, dynamic> json) {
    return MultiplayerMessage(
      event: MultiplayerEvent.values.firstWhere((e) => e.name == json['event']),
      senderId: json['senderId'],
      data: Map<String, dynamic>.from(json['data']),
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

abstract class MultiplayerService {
  Stream<MultiplayerMessage> get messageStream;
  bool get isConnected;
  String? get roomCode;
  
  Future<void> connect();
  Future<void> disconnect();
  Future<String> createRoom(GameState gameState);
  Future<GameState?> joinRoom(String roomCode);
  Future<void> leaveRoom();
  Future<void> sendMessage(MultiplayerMessage message);
  
  // Convenience methods
  Future<void> sendGameStateUpdate(GameState gameState);
  Future<void> sendPlayerMove(String playerId, int x, int y);
  Future<void> sendPlayerInteraction(String playerId, int x, int y, String action);
  Future<void> sendRoleSwap();
  Future<void> sendChatMessage(String playerId, String message);
}

class LocalMultiplayerService extends MultiplayerService {
  final StreamController<MultiplayerMessage> _messageController = 
      StreamController<MultiplayerMessage>.broadcast();
  
  final Map<String, GameState> _rooms = {};
  String? _currentRoomCode;
  bool _isConnected = false;

  @override
  Stream<MultiplayerMessage> get messageStream => _messageController.stream;
  
  @override
  bool get isConnected => _isConnected;
  
  @override
  String? get roomCode => _currentRoomCode;

  @override
  Future<void> connect() async {
    _isConnected = true;
    if (kDebugMode) {
      print('LocalMultiplayerService: Connected');
    }
  }

  @override
  Future<void> disconnect() async {
    _isConnected = false;
    _currentRoomCode = null;
    _rooms.clear();
    if (kDebugMode) {
      print('LocalMultiplayerService: Disconnected');
    }
  }

  @override
  Future<String> createRoom(GameState gameState) async {
    if (!_isConnected) {
      throw Exception('Not connected');
    }

    final roomCode = _generateRoomCode();
    _rooms[roomCode] = gameState;
    _currentRoomCode = roomCode;
    
    if (kDebugMode) {
      print('LocalMultiplayerService: Created room $roomCode');
    }
    
    return roomCode;
  }

  @override
  Future<GameState?> joinRoom(String roomCode) async {
    if (!_isConnected) {
      throw Exception('Not connected');
    }

    final gameState = _rooms[roomCode];
    if (gameState != null) {
      _currentRoomCode = roomCode;
      
      if (kDebugMode) {
        print('LocalMultiplayerService: Joined room $roomCode');
      }
    }
    
    return gameState;
  }

  @override
  Future<void> leaveRoom() async {
    if (_currentRoomCode != null) {
      _rooms.remove(_currentRoomCode);
      _currentRoomCode = null;
      
      if (kDebugMode) {
        print('LocalMultiplayerService: Left room');
      }
    }
  }

  @override
  Future<void> sendMessage(MultiplayerMessage message) async {
    if (!_isConnected) {
      throw Exception('Not connected');
    }

    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 10));
    
    _messageController.add(message);
    
    if (kDebugMode) {
      print('LocalMultiplayerService: Sent ${message.event.name}');
    }
  }

  @override
  Future<void> sendGameStateUpdate(GameState gameState) async {
    await sendMessage(MultiplayerMessage(
      event: MultiplayerEvent.gameStateUpdated,
      senderId: 'system',
      data: {'gameState': gameState.toJson()},
    ));
  }

  @override
  Future<void> sendPlayerMove(String playerId, int x, int y) async {
    await sendMessage(MultiplayerMessage(
      event: MultiplayerEvent.playerMoved,
      senderId: playerId,
      data: {'x': x, 'y': y},
    ));
  }

  @override
  Future<void> sendPlayerInteraction(String playerId, int x, int y, String action) async {
    await sendMessage(MultiplayerMessage(
      event: MultiplayerEvent.playerInteracted,
      senderId: playerId,
      data: {'x': x, 'y': y, 'action': action},
    ));
  }

  @override
  Future<void> sendRoleSwap() async {
    await sendMessage(MultiplayerMessage(
      event: MultiplayerEvent.roleSwapped,
      senderId: 'system',
      data: {},
    ));
  }

  @override
  Future<void> sendChatMessage(String playerId, String message) async {
    await sendMessage(MultiplayerMessage(
      event: MultiplayerEvent.chatMessage,
      senderId: playerId,
      data: {'message': message},
    ));
  }

  String _generateRoomCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();
    return String.fromCharCodes(
      Iterable.generate(6, (_) => chars.codeUnitAt(random.nextInt(chars.length)))
    );
  }

  void dispose() {
    _messageController.close();
  }
}

// Firebase implementation placeholder
class FirebaseMultiplayerService extends MultiplayerService {
  final StreamController<MultiplayerMessage> _messageController = 
      StreamController<MultiplayerMessage>.broadcast();
  
  String? _currentRoomCode;
  bool _isConnected = false;

  @override
  Stream<MultiplayerMessage> get messageStream => _messageController.stream;
  
  @override
  bool get isConnected => _isConnected;
  
  @override
  String? get roomCode => _currentRoomCode;

  @override
  Future<void> connect() async {
    // TODO: Initialize Firebase
    throw UnimplementedError('Firebase implementation not ready');
  }

  @override
  Future<void> disconnect() async {
    // TODO: Disconnect from Firebase
    _isConnected = false;
    _currentRoomCode = null;
  }

  @override
  Future<String> createRoom(GameState gameState) async {
    // TODO: Create Firebase room
    throw UnimplementedError('Firebase implementation not ready');
  }

  @override
  Future<GameState?> joinRoom(String roomCode) async {
    // TODO: Join Firebase room
    throw UnimplementedError('Firebase implementation not ready');
  }

  @override
  Future<void> leaveRoom() async {
    // TODO: Leave Firebase room
    _currentRoomCode = null;
  }

  @override
  Future<void> sendMessage(MultiplayerMessage message) async {
    // TODO: Send message via Firebase
    throw UnimplementedError('Firebase implementation not ready');
  }

  @override
  Future<void> sendGameStateUpdate(GameState gameState) async {
    await sendMessage(MultiplayerMessage(
      event: MultiplayerEvent.gameStateUpdated,
      senderId: 'system',
      data: {'gameState': gameState.toJson()},
    ));
  }

  @override
  Future<void> sendPlayerMove(String playerId, int x, int y) async {
    await sendMessage(MultiplayerMessage(
      event: MultiplayerEvent.playerMoved,
      senderId: playerId,
      data: {'x': x, 'y': y},
    ));
  }

  @override
  Future<void> sendPlayerInteraction(String playerId, int x, int y, String action) async {
    await sendMessage(MultiplayerMessage(
      event: MultiplayerEvent.playerInteracted,
      senderId: playerId,
      data: {'x': x, 'y': y, 'action': action},
    ));
  }

  @override
  Future<void> sendRoleSwap() async {
    await sendMessage(MultiplayerMessage(
      event: MultiplayerEvent.roleSwapped,
      senderId: 'system',
      data: {},
    ));
  }

  @override
  Future<void> sendChatMessage(String playerId, String message) async {
    await sendMessage(MultiplayerMessage(
      event: MultiplayerEvent.chatMessage,
      senderId: playerId,
      data: {'message': message},
    ));
  }

  void dispose() {
    _messageController.close();
  }
}

// Room management service
class RoomManager {
  final MultiplayerService _multiplayerService;
  final StreamController<GameState> _gameStateController =
      StreamController<GameState>.broadcast();

  GameState? _currentGameState;
  StreamSubscription<MultiplayerMessage>? _messageSubscription;

  RoomManager(this._multiplayerService) {
    _messageSubscription = _multiplayerService.messageStream.listen(_handleMessage);
  }

  Stream<GameState> get gameStateStream => _gameStateController.stream;
  GameState? get currentGameState => _currentGameState;
  bool get isConnected => _multiplayerService.isConnected;
  String? get roomCode => _multiplayerService.roomCode;

  Future<void> connect() async {
    await _multiplayerService.connect();
  }

  Future<void> disconnect() async {
    await _multiplayerService.disconnect();
    _currentGameState = null;
  }

  Future<String> createRoom(GameState gameState) async {
    final roomCode = await _multiplayerService.createRoom(gameState);
    _currentGameState = gameState;
    _gameStateController.add(gameState);
    return roomCode;
  }

  Future<bool> joinRoom(String roomCode) async {
    final gameState = await _multiplayerService.joinRoom(roomCode);
    if (gameState != null) {
      _currentGameState = gameState;
      _gameStateController.add(gameState);
      return true;
    }
    return false;
  }

  Future<void> leaveRoom() async {
    await _multiplayerService.leaveRoom();
    _currentGameState = null;
  }

  Future<void> updateGameState(GameState gameState) async {
    _currentGameState = gameState;
    _gameStateController.add(gameState);
    await _multiplayerService.sendGameStateUpdate(gameState);
  }

  Future<void> sendPlayerMove(String playerId, int x, int y) async {
    await _multiplayerService.sendPlayerMove(playerId, x, y);
  }

  Future<void> sendPlayerInteraction(String playerId, int x, int y, String action) async {
    await _multiplayerService.sendPlayerInteraction(playerId, x, y, action);
  }

  Future<void> sendRoleSwap() async {
    await _multiplayerService.sendRoleSwap();
  }

  Future<void> sendChatMessage(String playerId, String message) async {
    await _multiplayerService.sendChatMessage(playerId, message);
  }

  void _handleMessage(MultiplayerMessage message) {
    switch (message.event) {
      case MultiplayerEvent.gameStateUpdated:
        final gameStateData = message.data['gameState'] as Map<String, dynamic>;
        _currentGameState = GameState.fromJson(gameStateData);
        _gameStateController.add(_currentGameState!);
        break;
      case MultiplayerEvent.playerMoved:
        _handlePlayerMove(message);
        break;
      case MultiplayerEvent.playerInteracted:
        _handlePlayerInteraction(message);
        break;
      case MultiplayerEvent.roleSwapped:
        _handleRoleSwap(message);
        break;
      default:
        if (kDebugMode) {
          print('RoomManager: Unhandled message ${message.event.name}');
        }
        break;
    }
  }

  void _handlePlayerMove(MultiplayerMessage message) {
    if (_currentGameState == null) return;

    final playerId = message.senderId;
    final x = message.data['x'] as int;
    final y = message.data['y'] as int;

    final player = _currentGameState!.getPlayerById(playerId);
    if (player != null) {
      player.x = x;
      player.y = y;
      _gameStateController.add(_currentGameState!);
    }
  }

  void _handlePlayerInteraction(MultiplayerMessage message) {
    if (_currentGameState == null) return;

    final playerId = message.senderId;
    final x = message.data['x'] as int;
    final y = message.data['y'] as int;
    final action = message.data['action'] as String;

    // Process interaction based on action type
    // This would integrate with the GameInteractionService
    _gameStateController.add(_currentGameState!);
  }

  void _handleRoleSwap(MultiplayerMessage message) {
    if (_currentGameState == null) return;

    // Swap roles for all players
    for (final player in _currentGameState!.players) {
      player.swapRole();
    }

    _currentGameState!.totalRoleSwaps++;
    _gameStateController.add(_currentGameState!);
  }

  void dispose() {
    _messageSubscription?.cancel();
    _gameStateController.close();
  }
}

// Factory for creating multiplayer services
class MultiplayerServiceFactory {
  static MultiplayerService createService({bool useFirebase = false}) {
    if (useFirebase) {
      return FirebaseMultiplayerService();
    } else {
      return LocalMultiplayerService();
    }
  }

  static RoomManager createRoomManager({bool useFirebase = false}) {
    final service = createService(useFirebase: useFirebase);
    return RoomManager(service);
  }
}
