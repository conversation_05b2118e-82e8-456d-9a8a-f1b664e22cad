import '../models/models.dart';

class MazeValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;
  final Map<String, dynamic> metrics;

  const MazeValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
    required this.metrics,
  });

  bool get hasErrors => errors.isNotEmpty;
  bool get hasWarnings => warnings.isNotEmpty;
}

class MazeValidator {
  static MazeValidationResult validate(Maze maze) {
    final errors = <String>[];
    final warnings = <String>[];
    final metrics = <String, dynamic>{};

    // Basic structure validation
    _validateBasicStructure(maze, errors);
    
    // Path validation
    _validatePaths(maze, errors, warnings, metrics);
    
    // Element validation
    _validateElements(maze, errors, warnings, metrics);
    
    // Difficulty validation
    _validateDifficulty(maze, warnings, metrics);
    
    // Balance validation
    _validateBalance(maze, warnings, metrics);

    return MazeValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
      metrics: metrics,
    );
  }

  static void _validateBasicStructure(Maze maze, List<String> errors) {
    // Check minimum size
    if (maze.width < 5 || maze.height < 5) {
      errors.add('Maze too small: minimum size is 5x5');
    }

    // Check maximum size
    if (maze.width > 15 || maze.height > 15) {
      errors.add('Maze too large: maximum size is 15x15');
    }

    // Check if start position is valid
    if (!maze.isValidPosition(maze.startPosition.x, maze.startPosition.y)) {
      errors.add('Invalid start position: ${maze.startPosition}');
    }

    // Check if exit position is valid
    if (!maze.isValidPosition(maze.exitPosition.x, maze.exitPosition.y)) {
      errors.add('Invalid exit position: ${maze.exitPosition}');
    }

    // Check if start and exit are different
    if (maze.startPosition == maze.exitPosition) {
      errors.add('Start and exit positions cannot be the same');
    }

    // Check perimeter walls
    _validatePerimeterWalls(maze, errors);
  }

  static void _validatePerimeterWalls(Maze maze, List<String> errors) {
    // Check top and bottom walls
    for (int x = 0; x < maze.width; x++) {
      if (maze.getElementAt(x, 0).type != PuzzleElementType.wall) {
        errors.add('Missing wall at top perimeter: ($x, 0)');
      }
      if (maze.getElementAt(x, maze.height - 1).type != PuzzleElementType.wall) {
        errors.add('Missing wall at bottom perimeter: ($x, ${maze.height - 1})');
      }
    }

    // Check left and right walls
    for (int y = 0; y < maze.height; y++) {
      if (maze.getElementAt(0, y).type != PuzzleElementType.wall) {
        errors.add('Missing wall at left perimeter: (0, $y)');
      }
      if (maze.getElementAt(maze.width - 1, y).type != PuzzleElementType.wall) {
        errors.add('Missing wall at right perimeter: (${maze.width - 1}, $y)');
      }
    }
  }

  static void _validatePaths(
    Maze maze, 
    List<String> errors, 
    List<String> warnings,
    Map<String, dynamic> metrics
  ) {
    // Check if there's a path from start to exit
    final pathExists = _hasPath(maze, maze.startPosition, maze.exitPosition);
    if (!pathExists) {
      errors.add('No path exists from start to exit');
    }

    // Calculate path metrics
    final shortestPath = _findShortestPath(maze);
    metrics['shortestPathLength'] = shortestPath?.length ?? -1;
    metrics['hasPath'] = pathExists;

    // Check if path is too short or too long
    if (shortestPath != null) {
      final pathLength = shortestPath.length;
      final minExpectedLength = (maze.width + maze.height) ~/ 2;
      final maxExpectedLength = maze.width * maze.height ~/ 3;

      if (pathLength < minExpectedLength) {
        warnings.add('Path might be too short: $pathLength steps');
      } else if (pathLength > maxExpectedLength) {
        warnings.add('Path might be too long: $pathLength steps');
      }
    }

    // Check for isolated areas
    final isolatedAreas = _findIsolatedAreas(maze);
    metrics['isolatedAreas'] = isolatedAreas.length;
    if (isolatedAreas.isNotEmpty) {
      warnings.add('Found ${isolatedAreas.length} isolated areas');
    }
  }

  static void _validateElements(
    Maze maze, 
    List<String> errors, 
    List<String> warnings,
    Map<String, dynamic> metrics
  ) {
    final elementCounts = <PuzzleElementType, int>{};
    final keyDoorPairs = <String, List<PuzzleElement>>{};

    // Count elements and validate placement
    for (int y = 0; y < maze.height; y++) {
      for (int x = 0; x < maze.width; x++) {
        final element = maze.getElementAt(x, y);
        elementCounts[element.type] = (elementCounts[element.type] ?? 0) + 1;

        // Validate specific elements
        _validateElement(element, maze, errors, warnings);

        // Track key-door pairs
        if (element.type == PuzzleElementType.key) {
          final keyId = element.properties['keyId'] as String;
          keyDoorPairs.putIfAbsent(keyId, () => []).add(element);
        } else if (element.type == PuzzleElementType.door) {
          final keyId = element.properties['requiredKeyId'] as String;
          keyDoorPairs.putIfAbsent(keyId, () => []).add(element);
        }
      }
    }

    metrics['elementCounts'] = elementCounts;

    // Validate key-door pairs
    _validateKeyDoorPairs(keyDoorPairs, errors, warnings);

    // Check element distribution
    _validateElementDistribution(maze, elementCounts, warnings);
  }

  static void _validateElement(
    PuzzleElement element, 
    Maze maze, 
    List<String> errors, 
    List<String> warnings
  ) {
    switch (element.type) {
      case PuzzleElementType.key:
        if (!element.properties.containsKey('keyId')) {
          errors.add('Key at (${element.x}, ${element.y}) missing keyId');
        }
        break;
      case PuzzleElementType.door:
        if (!element.properties.containsKey('requiredKeyId')) {
          errors.add('Door at (${element.x}, ${element.y}) missing requiredKeyId');
        }
        break;
      case PuzzleElementType.trap:
        // Check if trap is in a dead end (might be unfair)
        if (_isInDeadEnd(maze, element.x, element.y)) {
          warnings.add('Trap at (${element.x}, ${element.y}) is in a dead end');
        }
        break;
      case PuzzleElementType.exit:
        // Ensure exit is accessible
        if (!maze.isWalkable(element.x, element.y)) {
          errors.add('Exit at (${element.x}, ${element.y}) is not walkable');
        }
        break;
      default:
        break;
    }
  }

  static void _validateKeyDoorPairs(
    Map<String, List<PuzzleElement>> keyDoorPairs,
    List<String> errors,
    List<String> warnings
  ) {
    for (final entry in keyDoorPairs.entries) {
      final keyId = entry.key;
      final elements = entry.value;
      
      final keys = elements.where((e) => e.type == PuzzleElementType.key).toList();
      final doors = elements.where((e) => e.type == PuzzleElementType.door).toList();

      if (keys.isEmpty) {
        errors.add('Door requires key "$keyId" but no such key exists');
      } else if (keys.length > 1) {
        warnings.add('Multiple keys found for keyId "$keyId"');
      }

      if (doors.isEmpty) {
        warnings.add('Key "$keyId" exists but no door requires it');
      } else if (doors.length > 1) {
        warnings.add('Multiple doors require key "$keyId"');
      }
    }
  }

  static void _validateElementDistribution(
    Maze maze,
    Map<PuzzleElementType, int> elementCounts,
    List<String> warnings
  ) {
    final totalCells = maze.width * maze.height;
    final emptyCells = elementCounts[PuzzleElementType.empty] ?? 0;
    final wallCells = elementCounts[PuzzleElementType.wall] ?? 0;

    // Check wall density
    final wallDensity = wallCells / totalCells;
    if (wallDensity > 0.7) {
      warnings.add('High wall density: ${(wallDensity * 100).toStringAsFixed(1)}%');
    } else if (wallDensity < 0.3) {
      warnings.add('Low wall density: ${(wallDensity * 100).toStringAsFixed(1)}%');
    }

    // Check puzzle element density
    final puzzleElements = elementCounts.values.fold(0, (sum, count) => sum + count) 
        - emptyCells - wallCells;
    final puzzleDensity = puzzleElements / emptyCells;
    
    if (puzzleDensity > 0.5) {
      warnings.add('High puzzle element density: ${(puzzleDensity * 100).toStringAsFixed(1)}%');
    }
  }

  static void _validateDifficulty(
    Maze maze, 
    List<String> warnings,
    Map<String, dynamic> metrics
  ) {
    final expectedCounts = _getExpectedElementCounts(maze.difficulty);
    final actualCounts = <PuzzleElementType, int>{};

    // Count actual elements
    for (int y = 0; y < maze.height; y++) {
      for (int x = 0; x < maze.width; x++) {
        final element = maze.getElementAt(x, y);
        actualCounts[element.type] = (actualCounts[element.type] ?? 0) + 1;
      }
    }

    // Compare with expected counts
    for (final entry in expectedCounts.entries) {
      final type = entry.key;
      final expected = entry.value;
      final actual = actualCounts[type] ?? 0;

      if (actual < expected['min']!) {
        warnings.add('Too few ${type.name}s: $actual (expected at least ${expected['min']})');
      } else if (actual > expected['max']!) {
        warnings.add('Too many ${type.name}s: $actual (expected at most ${expected['max']})');
      }
    }

    metrics['difficultyAlignment'] = _calculateDifficultyAlignment(maze, expectedCounts, actualCounts);
  }

  static Map<PuzzleElementType, Map<String, int>> _getExpectedElementCounts(MazeDifficulty difficulty) {
    switch (difficulty) {
      case MazeDifficulty.easy:
        return {
          PuzzleElementType.key: {'min': 1, 'max': 2},
          PuzzleElementType.door: {'min': 1, 'max': 2},
          PuzzleElementType.trap: {'min': 1, 'max': 3},
          PuzzleElementType.pressurePlate: {'min': 0, 'max': 2},
        };
      case MazeDifficulty.medium:
        return {
          PuzzleElementType.key: {'min': 2, 'max': 3},
          PuzzleElementType.door: {'min': 2, 'max': 3},
          PuzzleElementType.trap: {'min': 2, 'max': 4},
          PuzzleElementType.pressurePlate: {'min': 1, 'max': 3},
        };
      case MazeDifficulty.hard:
        return {
          PuzzleElementType.key: {'min': 3, 'max': 4},
          PuzzleElementType.door: {'min': 3, 'max': 4},
          PuzzleElementType.trap: {'min': 3, 'max': 6},
          PuzzleElementType.pressurePlate: {'min': 2, 'max': 4},
        };
    }
  }

  static double _calculateDifficultyAlignment(
    Maze maze,
    Map<PuzzleElementType, Map<String, int>> expected,
    Map<PuzzleElementType, int> actual
  ) {
    double score = 1.0;
    int totalChecks = 0;
    int passedChecks = 0;

    for (final entry in expected.entries) {
      final type = entry.key;
      final range = entry.value;
      final count = actual[type] ?? 0;

      totalChecks++;
      if (count >= range['min']! && count <= range['max']!) {
        passedChecks++;
      }
    }

    return totalChecks > 0 ? passedChecks / totalChecks : 0.0;
  }

  static void _validateBalance(
    Maze maze, 
    List<String> warnings,
    Map<String, dynamic> metrics
  ) {
    // Check time limit vs maze complexity
    final complexity = _calculateMazeComplexity(maze);
    metrics['complexity'] = complexity;

    final timePerComplexity = maze.timeLimit / complexity;
    if (timePerComplexity < 10) {
      warnings.add('Time limit might be too strict for maze complexity');
    } else if (timePerComplexity > 30) {
      warnings.add('Time limit might be too generous for maze complexity');
    }

    metrics['timePerComplexity'] = timePerComplexity;
  }

  static double _calculateMazeComplexity(Maze maze) {
    double complexity = 0.0;

    // Base complexity from size
    complexity += maze.width * maze.height * 0.1;

    // Add complexity for each element type
    for (int y = 0; y < maze.height; y++) {
      for (int x = 0; x < maze.width; x++) {
        final element = maze.getElementAt(x, y);
        switch (element.type) {
          case PuzzleElementType.key:
            complexity += 5.0;
            break;
          case PuzzleElementType.door:
            complexity += 3.0;
            break;
          case PuzzleElementType.trap:
            complexity += 4.0;
            break;
          case PuzzleElementType.pressurePlate:
            complexity += 2.0;
            break;
          case PuzzleElementType.mirrorTile:
            complexity += 6.0;
            break;
          case PuzzleElementType.fogZone:
            complexity += 8.0;
            break;
          default:
            break;
        }
      }
    }

    return complexity;
  }

  // Helper methods
  static bool _hasPath(Maze maze, Position start, Position end) {
    final visited = List.generate(maze.height, (_) => List.filled(maze.width, false));
    return _dfs(maze, start.x, start.y, end.x, end.y, visited);
  }

  static bool _dfs(Maze maze, int x, int y, int endX, int endY, List<List<bool>> visited) {
    if (x == endX && y == endY) return true;
    if (!maze.isValidPosition(x, y) || visited[y][x] || !maze.isWalkable(x, y)) {
      return false;
    }

    visited[y][x] = true;

    return _dfs(maze, x + 1, y, endX, endY, visited) ||
           _dfs(maze, x - 1, y, endX, endY, visited) ||
           _dfs(maze, x, y + 1, endX, endY, visited) ||
           _dfs(maze, x, y - 1, endX, endY, visited);
  }

  static List<Position>? _findShortestPath(Maze maze) {
    // Simple BFS implementation for shortest path
    final queue = <List<Position>>[];
    final visited = List.generate(maze.height, (_) => List.filled(maze.width, false));
    
    queue.add([maze.startPosition]);
    visited[maze.startPosition.y][maze.startPosition.x] = true;

    while (queue.isNotEmpty) {
      final path = queue.removeAt(0);
      final current = path.last;

      if (current == maze.exitPosition) {
        return path;
      }

      final directions = [
        Position(current.x + 1, current.y),
        Position(current.x - 1, current.y),
        Position(current.x, current.y + 1),
        Position(current.x, current.y - 1),
      ];

      for (final next in directions) {
        if (maze.isValidPosition(next.x, next.y) && 
            !visited[next.y][next.x] && 
            maze.isWalkable(next.x, next.y)) {
          visited[next.y][next.x] = true;
          queue.add([...path, next]);
        }
      }
    }

    return null;
  }

  static List<List<Position>> _findIsolatedAreas(Maze maze) {
    final visited = List.generate(maze.height, (_) => List.filled(maze.width, false));
    final areas = <List<Position>>[];

    for (int y = 0; y < maze.height; y++) {
      for (int x = 0; x < maze.width; x++) {
        if (!visited[y][x] && maze.isWalkable(x, y)) {
          final area = <Position>[];
          _floodFill(maze, x, y, visited, area);
          if (area.isNotEmpty) {
            areas.add(area);
          }
        }
      }
    }

    return areas;
  }

  static void _floodFill(
    Maze maze, 
    int x, 
    int y, 
    List<List<bool>> visited, 
    List<Position> area
  ) {
    if (!maze.isValidPosition(x, y) || visited[y][x] || !maze.isWalkable(x, y)) {
      return;
    }

    visited[y][x] = true;
    area.add(Position(x, y));

    _floodFill(maze, x + 1, y, visited, area);
    _floodFill(maze, x - 1, y, visited, area);
    _floodFill(maze, x, y + 1, visited, area);
    _floodFill(maze, x, y - 1, visited, area);
  }

  static bool _isInDeadEnd(Maze maze, int x, int y) {
    if (!maze.isWalkable(x, y)) return false;

    int walkableNeighbors = 0;
    final directions = [
      Position(x + 1, y),
      Position(x - 1, y),
      Position(x, y + 1),
      Position(x, y - 1),
    ];

    for (final pos in directions) {
      if (maze.isValidPosition(pos.x, pos.y) && maze.isWalkable(pos.x, pos.y)) {
        walkableNeighbors++;
      }
    }

    return walkableNeighbors <= 1;
  }
}
