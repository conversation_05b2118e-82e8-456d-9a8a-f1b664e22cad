import 'package:uuid/uuid.dart';

enum PlayerRole {
  navigator,
  walker,
}

enum PlayerStatus {
  waiting,
  connected,
  disconnected,
  ready,
}

class Player {
  final String id;
  final String name;
  PlayerRole role;
  PlayerStatus status;
  int x;
  int y;
  int lives;
  List<String> inventory;
  bool isHost;

  Player({
    String? id,
    required this.name,
    this.role = PlayerRole.walker,
    this.status = PlayerStatus.waiting,
    this.x = 0,
    this.y = 0,
    this.lives = 3,
    List<String>? inventory,
    this.isHost = false,
  }) : id = id ?? const Uuid().v4(),
       inventory = inventory ?? [];

  Player copyWith({
    String? id,
    String? name,
    PlayerRole? role,
    PlayerStatus? status,
    int? x,
    int? y,
    int? lives,
    List<String>? inventory,
    bool? isHost,
  }) {
    return Player(
      id: id ?? this.id,
      name: name ?? this.name,
      role: role ?? this.role,
      status: status ?? this.status,
      x: x ?? this.x,
      y: y ?? this.y,
      lives: lives ?? this.lives,
      inventory: inventory ?? List.from(this.inventory),
      isHost: isHost ?? this.isHost,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'role': role.name,
      'status': status.name,
      'x': x,
      'y': y,
      'lives': lives,
      'inventory': inventory,
      'isHost': isHost,
    };
  }

  factory Player.fromJson(Map<String, dynamic> json) {
    return Player(
      id: json['id'],
      name: json['name'],
      role: PlayerRole.values.firstWhere((e) => e.name == json['role']),
      status: PlayerStatus.values.firstWhere((e) => e.name == json['status']),
      x: json['x'],
      y: json['y'],
      lives: json['lives'],
      inventory: List<String>.from(json['inventory'] ?? []),
      isHost: json['isHost'] ?? false,
    );
  }

  bool hasKey(String keyId) {
    return inventory.contains(keyId);
  }

  void addToInventory(String item) {
    if (!inventory.contains(item)) {
      inventory.add(item);
    }
  }

  void removeFromInventory(String item) {
    inventory.remove(item);
  }

  void loseLife() {
    if (lives > 0) {
      lives--;
    }
  }

  bool get isAlive => lives > 0;

  void swapRole() {
    role = role == PlayerRole.navigator ? PlayerRole.walker : PlayerRole.navigator;
  }

  @override
  String toString() {
    return 'Player(id: $id, name: $name, role: $role, position: ($x, $y), lives: $lives)';
  }
}
