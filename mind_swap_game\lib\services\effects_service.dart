import 'dart:async';
import 'package:flutter/material.dart';
import '../widgets/simple_effects.dart';

enum EffectType {
  keyCollection,
  trapExplosion,
  doorUnlock,
  victory,
  gameOver,
  roleSwap,
  pressurePlate,
  warningFlash,
}

class EffectRequest {
  final String id;
  final EffectType type;
  final Offset? position;
  final Size? screenSize;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  EffectRequest({
    required this.id,
    required this.type,
    this.position,
    this.screenSize,
    this.data = const {},
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();
}

class EffectsService extends ChangeNotifier {
  final List<EffectRequest> _activeEffects = [];
  final StreamController<EffectRequest> _effectController = 
      StreamController<EffectRequest>.broadcast();

  // Getters
  List<EffectRequest> get activeEffects => List.unmodifiable(_activeEffects);
  Stream<EffectRequest> get effectStream => _effectController.stream;

  // Effect creation methods
  void playKeyCollectionEffect({
    required String id,
    required Offset position,
  }) {
    final effect = EffectRequest(
      id: id,
      type: EffectType.keyCollection,
      position: position,
    );
    
    _addEffect(effect);
  }

  void playTrapExplosionEffect({
    required String id,
    required Offset position,
  }) {
    final effect = EffectRequest(
      id: id,
      type: EffectType.trapExplosion,
      position: position,
    );
    
    _addEffect(effect);
  }

  void playDoorUnlockEffect({
    required String id,
    required Offset position,
  }) {
    final effect = EffectRequest(
      id: id,
      type: EffectType.doorUnlock,
      position: position,
    );
    
    _addEffect(effect);
  }

  void playVictoryEffect({
    required String id,
    required Size screenSize,
  }) {
    final effect = EffectRequest(
      id: id,
      type: EffectType.victory,
      screenSize: screenSize,
    );
    
    _addEffect(effect);
  }

  void playGameOverEffect({
    required String id,
    required Size screenSize,
  }) {
    final effect = EffectRequest(
      id: id,
      type: EffectType.gameOver,
      screenSize: screenSize,
    );
    
    _addEffect(effect);
  }

  void playRoleSwapEffect({
    required String id,
    required Size screenSize,
    required String fromRole,
    required String toRole,
  }) {
    final effect = EffectRequest(
      id: id,
      type: EffectType.roleSwap,
      screenSize: screenSize,
      data: {
        'fromRole': fromRole,
        'toRole': toRole,
      },
    );
    
    _addEffect(effect);
  }

  void playPressurePlateEffect({
    required String id,
    required Offset position,
  }) {
    final effect = EffectRequest(
      id: id,
      type: EffectType.pressurePlate,
      position: position,
    );
    
    _addEffect(effect);
  }

  void playWarningFlashEffect({
    required String id,
    required Size screenSize,
  }) {
    final effect = EffectRequest(
      id: id,
      type: EffectType.warningFlash,
      screenSize: screenSize,
    );
    
    _addEffect(effect);
  }

  // Effect management
  void _addEffect(EffectRequest effect) {
    _activeEffects.add(effect);
    _effectController.add(effect);
    notifyListeners();
  }

  void removeEffect(String effectId) {
    _activeEffects.removeWhere((effect) => effect.id == effectId);
    notifyListeners();
  }

  void clearAllEffects() {
    _activeEffects.clear();
    notifyListeners();
  }

  // Widget creation methods
  Widget? createEffectWidget(EffectRequest effect) {
    switch (effect.type) {
      case EffectType.keyCollection:
        if (effect.position != null) {
          return GameEffects.keyCollectionEffect(
            position: effect.position!,
            onComplete: () => removeEffect(effect.id),
          );
        }
        break;
        
      case EffectType.trapExplosion:
        if (effect.position != null) {
          return GameEffects.trapExplosionEffect(
            position: effect.position!,
            onComplete: () => removeEffect(effect.id),
          );
        }
        break;
        
      case EffectType.doorUnlock:
        if (effect.position != null) {
          return GameEffects.doorUnlockEffect(
            position: effect.position!,
            onComplete: () => removeEffect(effect.id),
          );
        }
        break;
        
      case EffectType.victory:
        if (effect.screenSize != null) {
          return GameEffects.victoryEffect(
            screenSize: effect.screenSize!,
            onComplete: () => removeEffect(effect.id),
          );
        }
        break;
        
      case EffectType.gameOver:
        if (effect.screenSize != null) {
          return GameEffects.gameOverEffect(
            screenSize: effect.screenSize!,
            onComplete: () => removeEffect(effect.id),
          );
        }
        break;
        
      case EffectType.roleSwap:
        if (effect.screenSize != null) {
          return GameEffects.roleSwapTransition(
            screenSize: effect.screenSize!,
            fromRole: effect.data['fromRole'] ?? 'walker',
            toRole: effect.data['toRole'] ?? 'navigator',
            onComplete: () => removeEffect(effect.id),
          );
        }
        break;
        
      case EffectType.pressurePlate:
        if (effect.position != null) {
          return GameEffects.pressurePlateEffect(
            position: effect.position!,
            onComplete: () => removeEffect(effect.id),
          );
        }
        break;
        
      case EffectType.warningFlash:
        if (effect.screenSize != null) {
          return GameEffects.warningFlash(
            screenSize: effect.screenSize!,
            onComplete: () => removeEffect(effect.id),
          );
        }
        break;
    }
    
    return null;
  }

  @override
  void dispose() {
    _effectController.close();
    super.dispose();
  }
}

// Widget that displays all active effects
class EffectsOverlay extends StatelessWidget {
  final EffectsService effectsService;

  const EffectsOverlay({
    super.key,
    required this.effectsService,
  });

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: effectsService,
      builder: (context, child) {
        return Stack(
          children: effectsService.activeEffects
              .map((effect) => effectsService.createEffectWidget(effect))
              .where((widget) => widget != null)
              .cast<Widget>()
              .toList(),
        );
      },
    );
  }
}

// Convenience methods for common effect combinations
extension EffectsServiceExtensions on EffectsService {
  void playGameStartEffects(Size screenSize) {
    // Could play multiple effects for game start
    playWarningFlashEffect(
      id: 'game_start_flash',
      screenSize: screenSize,
    );
  }

  void playPlayerDeathEffects(Offset position, Size screenSize) {
    playTrapExplosionEffect(
      id: 'player_death_explosion',
      position: position,
    );
    
    // Add a warning flash after a delay
    Future.delayed(const Duration(milliseconds: 500), () {
      playWarningFlashEffect(
        id: 'player_death_flash',
        screenSize: screenSize,
      );
    });
  }

  void playLevelCompleteEffects(Size screenSize) {
    playVictoryEffect(
      id: 'level_complete',
      screenSize: screenSize,
    );
  }

  void playInteractionEffects({
    required String interactionType,
    required Offset position,
    Size? screenSize,
  }) {
    final effectId = '${interactionType}_${DateTime.now().millisecondsSinceEpoch}';
    
    switch (interactionType) {
      case 'key_collected':
        playKeyCollectionEffect(id: effectId, position: position);
        break;
      case 'door_unlocked':
        playDoorUnlockEffect(id: effectId, position: position);
        break;
      case 'trap_triggered':
        playTrapExplosionEffect(id: effectId, position: position);
        break;
      case 'pressure_plate_activated':
        playPressurePlateEffect(id: effectId, position: position);
        break;
    }
  }
}
